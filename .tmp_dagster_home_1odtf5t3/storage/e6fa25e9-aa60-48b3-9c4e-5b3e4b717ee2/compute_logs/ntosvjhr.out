drop backup table success
create backup table success
------ start process ZCRMR014 ------------
------ process ZCRMR014 -----fail------
Error: HTTPDriver for https://uat-clickhouse.datxanh.com.vn:443 received ClickHouse error code 179
 Code: 179. DB::Exception: Multiple table expressions with same alias o. In scope SELECT DISTINCT o.code AS orgchartCode, p.code AS projectCode, e.code AS employeeCode, c.name AS customerName, c.identity_code AS customerIdentityCode, t.booking_ticket_code AS bookingTicketCode, t.amount AS money, t.bank_account_number AS customerBankAccountNumber, t.bank_name AS customerBankName, t.created_date AS ticket_created_date, t.note AS note, t.date_key AS dateKey, d.full_date AS fullDate, d.date_string AS dateString, d.year AS year, d.quarter AS quarter, d.quarter_name AS quarterName, d.month AS month, d.month_name AS monthName, d.month_short_name AS monthShortName, d.week_of_year AS weekOfYear, d.day_of_week AS dayOfWeek, d.day_of_month AS dayOfMonth, d.day_of_year AS dayOfYear, d.day_name AS dayName, d.day_short_name AS dayShortName, d.fiscal_year AS fiscalYear, d.fiscal_quarter AS fiscalQuarter, d.fiscal_month AS fiscalMonth, d.is_weekend AS isWeekend, d.is_holiday AS isHoliday, d.is_business_day AS isBusinessDay, d.is_last_day_of_month AS isLastDayOfMonth, d.days_in_month AS daysInMonth, d.year_month_number AS yearMonthNumber, d.year_quarter_number AS yearQuarterNumber, d.previous_day_date_key AS previousDayDateKey, d.next_day_date_key AS nextDayDateKey FROM crm_report.fact_orders AS o LEFT JOIN crm_report.dim_demand_customers AS c ON (t.customer_code = c.code) AND (c.is_current = 1) LEFT JOIN crm_report.dim_orgcharts AS o ON (t.pos_id = o.id) AND (o.is_current = 1) LEFT JOIN crm_report.dim_projects AS p ON (t.project_id = p.id) AND (p.is_current = 1) LEFT JOIN crm_report.dim_employees AS e ON (t.employee_id = e.id) AND (e.is_current = 1) LEFT JOIN crm_report.dim_date AS d ON t.date_key = d.date_key WHERE (o.ticket_type = 'YCDCH') AND (o.status = 'CS_APPROVED_CANCEL_REQUESTED') AND (o.customer_code != '') AND (o.total_amount_refund = 0). (MULTIPLE_EXPRESSIONS_FOR_ALIAS) (version ********* (official build))

restore backup table success
------ restore backup table success------
