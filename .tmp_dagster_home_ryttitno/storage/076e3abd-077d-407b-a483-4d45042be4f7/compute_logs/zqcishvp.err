[32m2025-05-29 08:29:11 +0700[0m - dagster - [34mDEBUG[0m - __ASSET_JOB - 076e3abd-077d-407b-a483-4d45042be4f7 - 18840 - LOGS_CAPTURED - Started capturing logs in process (pid: 18840).
[32m2025-05-29 08:29:11 +0700[0m - dagster - [34mDEBUG[0m - __ASSET_JOB - 076e3abd-077d-407b-a483-4d45042be4f7 - 18840 - process_project_events - STEP_START - Started execution of step "process_project_events".
[32m2025-05-29 08:29:11 +0700[0m - dagster - [34mDEBUG[0m - __ASSET_JOB - 076e3abd-077d-407b-a483-4d45042be4f7 - 18840 - process_project_events - STEP_OUTPUT - Yielded output "result" of type "Any". (Type check passed).
[32m2025-05-29 08:29:12 +0700[0m - dagster - [34mDEBUG[0m - __ASSET_JOB - 076e3abd-077d-407b-a483-4d45042be4f7 - process_project_events - Writing file at: C:\Users\<USER>\PycharmProjects\dagster-etl\.tmp_dagster_home_ryttitno\storage\process_project_events using PickledObjectFilesystemIOManager...
[32m2025-05-29 08:29:12 +0700[0m - dagster - [34mDEBUG[0m - __ASSET_JOB - 076e3abd-077d-407b-a483-4d45042be4f7 - 18840 - process_project_events - ASSET_MATERIALIZATION - Materialized value process_project_events.
[32m2025-05-29 08:29:12 +0700[0m - dagster - [34mDEBUG[0m - __ASSET_JOB - 076e3abd-077d-407b-a483-4d45042be4f7 - 18840 - process_project_events - HANDLED_OUTPUT - Handled output "result" using IO manager "io_manager"
[32m2025-05-29 08:29:13 +0700[0m - dagster - [34mDEBUG[0m - __ASSET_JOB - 076e3abd-077d-407b-a483-4d45042be4f7 - 18840 - process_project_events - STEP_SUCCESS - Finished execution of step "process_project_events" in 1.23s.
