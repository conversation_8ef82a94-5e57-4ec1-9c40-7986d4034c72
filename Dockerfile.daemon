FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements files
COPY setup.py pyproject.toml ./

# Install Python dependencies
RUN pip install --no-cache-dir -e ".[dev]"

# Copy project files
COPY . .

# Create non-root user
RUN useradd -m dagster
RUN mkdir -p /home/<USER>/dagster_home && chown -R dagster:dagster /home/<USER>/dagster_home
USER dagster

# Set environment variables
ENV DAGSTER_HOME=/home/<USER>/dagster_home

# Copy config file
COPY config.yaml /home/<USER>/dagster_home/
COPY dagster.yaml /home/<USER>/dagster_home/

# Run the daemon
CMD ["dagster-daemon", "run"]