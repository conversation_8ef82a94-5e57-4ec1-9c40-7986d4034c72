create table ZCRMR001
(
    orgchartCode       String,
    employeeCode       String,
    projectCode        String,
    orgchartName       String,
    customerCode       String,
    propertyCode       String,
    price              Int128,
    commissionRate     UInt8,
    completedDate      DateTime,
    handoverDate       DateTime,
    status             String,
    note               String,
    projectName        String,
    dateKey            UInt32,
    fullDate           Date,
    dateString         String,
    year               UInt16,
    quarter            UInt8,
    quarterName        String,
    month              UInt8,
    monthName          String,
    monthShortName     String,
    weekOfYear         UInt8,
    dayOfWeek          UInt8,
    dayOfMonth         UInt8,
    dayOfYear          UInt16,
    dayName            String,
    dayShortName       String,
    fiscalYear         UInt16,
    fiscalQuarter      UInt8,
    fiscalMonth        UInt8,
    isWeekend          UInt8,
    isHoliday          UInt8,
    isBusinessDay      UInt8,
    isLastDayOfMonth   UInt8,
    daysInMonth        UInt8,
    yearMonthNumber    UInt32,
    yearQuarterNumber  UInt32,
    previousDayDateKey UInt32,
    nextDayDateKey     UInt32,
    customerName       Nullable(String)
)
    engine = MergeTree PRIMARY KEY (projectCode, orgchartCode, employeeCode, completedDate, status, dateKey)
        ORDER BY (projectCode, orgchartCode, employeeCode, completedDate, status, dateKey)
        SETTINGS index_granularity = 8192;

create table ZCRMR003
(
    orgchartCode          String,
    projectCode           String,
    employeeCode          String,
    projectName           String,
    customerName          String,
    customerIdentityCode  String,
    customerIdentityPlace String,
    customerIdentityDate  String,
    taxCode               String,
    bookingTicketCode     String,
    amount                Int64,
    status                String,
    orgchartName          String,
    employeeName          String,
    note                  String,
    dateKey               UInt32,
    fullDate              Date,
    dateString            String,
    year                  UInt16,
    quarter               UInt8,
    quarterName           String,
    month                 UInt8,
    monthName             String,
    monthShortName        String,
    weekOfYear            UInt8,
    dayOfWeek             UInt8,
    dayOfMonth            UInt8,
    dayOfYear             UInt16,
    dayName               String,
    dayShortName          String,
    fiscalYear            UInt16,
    fiscalQuarter         UInt8,
    fiscalMonth           UInt8,
    isWeekend             UInt8,
    isHoliday             UInt8,
    isBusinessDay         UInt8,
    isLastDayOfMonth      UInt8,
    daysInMonth           UInt8,
    yearMonthNumber       UInt32,
    yearQuarterNumber     UInt32,
    previousDayDateKey    UInt32,
    nextDayDateKey        UInt32
)
    engine = MergeTree PRIMARY KEY (projectCode, orgchartCode, employeeCode, status, dateKey)
        ORDER BY (projectCode, orgchartCode, employeeCode, status, dateKey)
        SETTINGS index_granularity = 8192;

create table ZCRMR004
(
    orgchartCode                  String,
    projectCode                   String,
    projectName                   String,
    propertyCode                  String,
    customerCode                  String,
    customerName                  String,
    customerIdentityCode          String,
    customerIdentityPlace         String,
    customerIdentityDate          String,
    bookingTicketCode             String,
    registeredConsultingFeePhase1 Int64,
    registeredConsultingFeePhase2 Int64,
    totalAmount                   Int64,
    orgchartName                  String,
    employeeCode                  String,
    employeeName                  String,
    note                          String,
    dateKey                       UInt32,
    fullDate                      Date,
    dateString                    String,
    year                          UInt16,
    quarter                       UInt8,
    quarterName                   String,
    month                         UInt8,
    monthName                     String,
    monthShortName                String,
    weekOfYear                    UInt8,
    dayOfWeek                     UInt8,
    dayOfMonth                    UInt8,
    dayOfYear                     UInt16,
    dayName                       String,
    dayShortName                  String,
    fiscalYear                    UInt16,
    fiscalQuarter                 UInt8,
    fiscalMonth                   UInt8,
    isWeekend                     UInt8,
    isHoliday                     UInt8,
    isBusinessDay                 UInt8,
    isLastDayOfMonth              UInt8,
    daysInMonth                   UInt8,
    yearMonthNumber               UInt32,
    yearQuarterNumber             UInt32,
    previousDayDateKey            UInt32,
    nextDayDateKey                UInt32
)
    engine = MergeTree PRIMARY KEY (projectCode, orgchartCode, employeeCode, dateKey)
        ORDER BY (projectCode, orgchartCode, employeeCode, dateKey)
        SETTINGS index_granularity = 8192;

create table ZCRMR005
(
    employeeCode              String,
    employeeName              String,
    customerCode              String,
    customerName              String,
    taxCode                   String,
    issueLocation             String,
    issueDate                 String,
    companyRepresentativeName String,
    customerBirthday          String,
    customerPhone             String,
    customerEmail             String,
    customerType              String,
    identityCode              String,
    bookingTicketCode         String,
    note                      String,
    transDate                 DateTime,
    status                    String,
    projectCode               String,
    propertyCode              String,
    propertyShortCode         String,
    orgchartName              String,
    orgchartCode              String,
    priority                  Int8,
    dateKey                   UInt32,
    fullDate                  Date,
    dateString                String,
    year                      UInt16,
    quarter                   UInt8,
    quarterName               String,
    month                     UInt8,
    monthName                 String,
    monthShortName            String,
    weekOfYear                UInt8,
    dayOfWeek                 UInt8,
    dayOfMonth                UInt8,
    dayOfYear                 UInt16,
    dayName                   String,
    dayShortName              String,
    fiscalYear                UInt16,
    fiscalQuarter             UInt8,
    fiscalMonth               UInt8,
    isWeekend                 UInt8,
    isHoliday                 UInt8,
    isBusinessDay             UInt8,
    isLastDayOfMonth          UInt8,
    daysInMonth               UInt8,
    yearMonthNumber           UInt32,
    yearQuarterNumber         UInt32,
    previousDayDateKey        UInt32,
    nextDayDateKey            UInt32,
    propertyIdentity          Nullable(String),
    projectName               String
)
    engine = MergeTree PRIMARY KEY (projectCode, orgchartCode, employeeCode, customerType, bookingTicketCode,
                                    customerEmail, status, transDate, dateKey)
        ORDER BY (projectCode, orgchartCode, employeeCode, customerType, bookingTicketCode, customerEmail, status,
                  transDate, dateKey)
        SETTINGS index_granularity = 8192;

create table ZCRMR006
(
    orgchartCode       String,
    orgchartName       String,
    projectCode        String,
    projectName        String,
    propertyCode       String,
    customerName       String,
    customerAddress    String,
    depositCount       UInt8,
    amount             Int128,
    employeeCode       String,
    note               String,
    dateKey            UInt32,
    fullDate           Date,
    dateString         String,
    year               UInt16,
    quarter            UInt8,
    quarterName        String,
    month              UInt8,
    monthName          String,
    monthShortName     String,
    weekOfYear         UInt8,
    dayOfWeek          UInt8,
    dayOfMonth         UInt8,
    dayOfYear          UInt16,
    dayName            String,
    dayShortName       String,
    fiscalYear         UInt16,
    fiscalQuarter      UInt8,
    fiscalMonth        UInt8,
    isWeekend          UInt8,
    isHoliday          UInt8,
    isBusinessDay      UInt8,
    isLastDayOfMonth   UInt8,
    daysInMonth        UInt8,
    yearMonthNumber    UInt32,
    yearQuarterNumber  UInt32,
    previousDayDateKey UInt32,
    nextDayDateKey     UInt32
)
    engine = MergeTree PRIMARY KEY (orgchartCode, orgchartName, projectCode, projectName, employeeCode, dateKey)
        ORDER BY (orgchartCode, orgchartName, projectCode, projectName, employeeCode, dateKey)
        SETTINGS index_granularity = 8192;

create table ZCRMR007
(
    propertyIdentity         String,
    propertyType             String,
    customerCode             String,
    customerName             String,
    nvatPrice                UInt64,
    transactionConsultingFee UInt64,
    priceTotal               UInt64,
    revenueRate              Float64,
    employeeRevenueRate      Float64,
    totalBasicFee            UInt64,
    totalFee                 UInt64,
    otherFee                 UInt64,
    totalAmount              UInt64,
    advanced                 UInt64,
    residual                 UInt64,
    escrowDate               DateTime,
    orgchartName             String,
    orgchartCode             String,
    dateKey                  UInt32,
    fullDate                 Date,
    dateString               String,
    year                     UInt16,
    quarter                  UInt8,
    quarterName              String,
    month                    UInt8,
    monthName                String,
    monthShortName           String,
    weekOfYear               UInt8,
    dayOfWeek                UInt8,
    dayOfMonth               UInt8,
    dayOfYear                UInt16,
    dayName                  String,
    dayShortName             String,
    fiscalYear               UInt16,
    fiscalQuarter            UInt8,
    fiscalMonth              UInt8,
    isWeekend                UInt8,
    isHoliday                UInt8,
    isBusinessDay            UInt8,
    isLastDayOfMonth         UInt8,
    daysInMonth              UInt8,
    yearMonthNumber          UInt32,
    yearQuarterNumber        UInt32,
    previousDayDateKey       UInt32,
    nextDayDateKey           UInt32,
    periodFrom               DateTime,
    periodTo                 DateTime,
    note                     Nullable(String),
    projectName              String,
    period                   String
)
    engine = MergeTree PRIMARY KEY (period, propertyIdentity, orgchartCode, totalFee, dateKey)
        ORDER BY (period, propertyIdentity, orgchartCode, totalFee, dateKey)
        SETTINGS index_granularity = 8192;

create table ZCRMR010_1
(
    propertyIdentity         String,
    propertyType             String,
    customerCode             String,
    customerName             String,
    nvatPrice                UInt64,
    transactionConsultingFee UInt64,
    referencePrice           UInt64,
    revenueRate              Float64,
    employeeRevenueRate      Float64,
    registerFee              UInt64,
    advance_1st              UInt64,
    ticketCreatedDate        DateTime comment 'ngày tạo phiếu đặt coc',
    orgchartName             String,
    orgchartCode             String,
    dateKey                  UInt32,
    fullDate                 Date,
    dateString               String,
    year                     UInt16,
    quarter                  UInt8,
    quarterName              String,
    month                    UInt8,
    monthName                String,
    monthShortName           String,
    weekOfYear               UInt8,
    dayOfWeek                UInt8,
    dayOfMonth               UInt8,
    dayOfYear                UInt16,
    dayName                  String,
    dayShortName             String,
    fiscalYear               UInt16,
    fiscalQuarter            UInt8,
    fiscalMonth              UInt8,
    isWeekend                UInt8,
    isHoliday                UInt8,
    isBusinessDay            UInt8,
    isLastDayOfMonth         UInt8,
    daysInMonth              UInt8,
    yearMonthNumber          UInt32,
    yearQuarterNumber        UInt32,
    previousDayDateKey       UInt32,
    nextDayDateKey           UInt32,
    periodFrom               DateTime,
    periodTo                 DateTime,
    price                    Nullable(UInt64) comment 'giá có vat',
    period                   String
)
    engine = MergeTree PRIMARY KEY (periodTo, periodFrom, propertyIdentity, orgchartCode, orgchartName, customerName,
                                    nvatPrice, transactionConsultingFee, referencePrice, advance_1st, dateKey)
        ORDER BY (periodTo, periodFrom, propertyIdentity, orgchartCode, orgchartName, customerName, nvatPrice,
                  transactionConsultingFee, referencePrice, advance_1st, dateKey)
        SETTINGS index_granularity = 8192;

create table ZCRMR010_2
(
    propertyIdentity         String,
    propertyType             String,
    customerCode             String,
    customerName             String,
    nvatPrice                UInt64,
    price                    UInt64,
    transactionConsultingFee Float64,
    referencePrice           UInt64,
    revenueRate              Float64,
    employeeRevenueRate      Float64,
    registerFee              UInt64,
    otherFee                 UInt64,
    advanced_1st             Float64,
    advance_2nd              Float64,
    ticketCreatedDate        String comment 'ngày tạo ticket',
    depositContractDate      String,
    orgchartName             String,
    orgchartCode             String,
    dateKey                  UInt32,
    periodFrom               DateTime,
    periodTo                 DateTime,
    fullDate                 Date,
    dateString               String,
    year                     UInt16,
    quarter                  UInt8,
    quarterName              String,
    month                    UInt8,
    monthName                String,
    monthShortName           String,
    weekOfYear               UInt8,
    dayOfWeek                UInt8,
    dayOfMonth               UInt8,
    dayOfYear                UInt16,
    dayName                  String,
    dayShortName             String,
    fiscalYear               UInt16,
    fiscalQuarter            UInt8,
    fiscalMonth              UInt8,
    isWeekend                UInt8,
    isHoliday                UInt8,
    isBusinessDay            UInt8,
    isLastDayOfMonth         UInt8,
    daysInMonth              UInt8,
    yearMonthNumber          UInt32,
    yearQuarterNumber        UInt32,
    previousDayDateKey       UInt32,
    nextDayDateKey           UInt32,
    period                   String
)
    engine = MergeTree PRIMARY KEY (period, propertyIdentity, orgchartCode, orgchartName, customerName, nvatPrice,
                                    transactionConsultingFee, referencePrice, dateKey)
        ORDER BY (period, propertyIdentity, orgchartCode, orgchartName, customerName, nvatPrice,
                  transactionConsultingFee, referencePrice, dateKey)
        SETTINGS index_granularity = 8192;

create table ZCRMR010_3
(
    propertyIdentity         String,
    propertyType             String,
    customerCode             String,
    customerName             String,
    nvatPrice                UInt64,
    price                    UInt64,
    transactionConsultingFee Float64,
    referencePrice           UInt64,
    revenueRate              Float64,
    employeeRevenueRate      Float64,
    registerFee              UInt64,
    otherFee                 UInt64,
    total                    UInt64,
    advanced                 Float64,
    advance_3th              Float64,
    ticketCreatedDate        String comment 'ngày tạo ticket',
    orgchartName             String,
    orgchartCode             String,
    dateKey                  UInt32,
    periodFrom               DateTime,
    periodTo                 DateTime,
    fullDate                 Date,
    dateString               String,
    year                     UInt16,
    quarter                  UInt8,
    quarterName              String,
    month                    UInt8,
    monthName                String,
    monthShortName           String,
    weekOfYear               UInt8,
    dayOfWeek                UInt8,
    dayOfMonth               UInt8,
    dayOfYear                UInt16,
    dayName                  String,
    dayShortName             String,
    fiscalYear               UInt16,
    fiscalQuarter            UInt8,
    fiscalMonth              UInt8,
    isWeekend                UInt8,
    isHoliday                UInt8,
    isBusinessDay            UInt8,
    isLastDayOfMonth         UInt8,
    daysInMonth              UInt8,
    yearMonthNumber          UInt32,
    yearQuarterNumber        UInt32,
    previousDayDateKey       UInt32,
    nextDayDateKey           UInt32,
    period                   String
)
    engine = MergeTree PRIMARY KEY (period, propertyIdentity, orgchartCode, orgchartName, customerName, nvatPrice,
                                    transactionConsultingFee, referencePrice, advanced, advance_3th, dateKey)
        ORDER BY (period, propertyIdentity, orgchartCode, orgchartName, customerName, nvatPrice,
                  transactionConsultingFee, referencePrice, advanced, advance_3th, dateKey)
        SETTINGS index_granularity = 8192;

create table ZCRMR014
(
    orgchartCode              String,
    projectCode               String,
    employeeCode              String,
    customerName              String,
    customerIdentityCode      String,
    bookingTicketCode         String,
    money                     UInt128,
    customerBankAccountNumber String,
    customerBankName          String,
    ticketCreatedDate         DateTime,
    note                      String,
    dateKey                   UInt32,
    fullDate                  Date,
    dateString                String,
    year                      UInt16,
    quarter                   UInt8,
    quarterName               String,
    month                     UInt8,
    monthName                 String,
    monthShortName            String,
    weekOfYear                UInt8,
    dayOfWeek                 UInt8,
    dayOfMonth                UInt8,
    dayOfYear                 UInt16,
    dayName                   String,
    dayShortName              String,
    fiscalYear                UInt16,
    fiscalQuarter             UInt8,
    fiscalMonth               UInt8,
    isWeekend                 UInt8,
    isHoliday                 UInt8,
    isBusinessDay             UInt8,
    isLastDayOfMonth          UInt8,
    daysInMonth               UInt8,
    yearMonthNumber           UInt32,
    yearQuarterNumber         UInt32,
    previousDayDateKey        UInt32,
    nextDayDateKey            UInt32
)
    engine = MergeTree PRIMARY KEY (orgchartCode, projectCode, employeeCode, dateKey, customerName, bookingTicketCode)
        ORDER BY (orgchartCode, projectCode, employeeCode, dateKey, customerName, bookingTicketCode)
        SETTINGS index_granularity = 8192;

create table ZCRMR015
(
    propertyUnitCode   String,
    propertyIdentity   Nullable(String),
    customerName       String,
    identityCode       String,
    identityDate       String,
    identityPlace      String,
    transDate          DateTime,
    totalAmountDeposit Int64,
    cancelReason       String,
    orgchartCode       String,
    orgchartName       String,
    employeeCode       String,
    projectCode        String,
    `o.status`         String,
    note               String,
    dateKey            UInt32,
    fullDate           Date,
    dateString         String,
    year               UInt16,
    quarter            UInt8,
    quarterName        String,
    month              UInt8,
    monthName          String,
    monthShortName     String,
    weekOfYear         UInt8,
    dayOfWeek          UInt8,
    dayOfMonth         UInt8,
    dayOfYear          UInt16,
    dayName            String,
    dayShortName       String,
    fiscalYear         UInt16,
    fiscalQuarter      UInt8,
    fiscalMonth        UInt8,
    isWeekend          UInt8,
    isHoliday          UInt8,
    isBusinessDay      UInt8,
    isLastDayOfMonth   UInt8,
    daysInMonth        UInt8,
    yearMonthNumber    UInt32,
    yearQuarterNumber  UInt32,
    previousDayDateKey UInt32,
    nextDayDateKey     UInt32
)
    engine = MergeTree PRIMARY KEY (orgchartCode, projectCode, employeeCode, dateKey, customerName, totalAmountDeposit)
        ORDER BY (orgchartCode, projectCode, employeeCode, dateKey, customerName, totalAmountDeposit)
        SETTINGS index_granularity = 8192;

create table ZCRMR017
(
    orgchartCode       String,
    orgchartName       String,
    projectCode        String,
    projectName        String,
    employeeCode       String,
    propertyUnitCode   String,
    propertyIdentity   Nullable(String),
    customerName       String,
    identityCode       String,
    customerPhone      String,
    depositDate        DateTime,
    amountDeposit      Int64,
    rate               UInt8,
    amountRate         Float64,
    cancelReason       String,
    dateKey            UInt32,
    fullDate           Date,
    dateString         String,
    year               UInt16,
    quarter            UInt8,
    quarterName        String,
    month              UInt8,
    monthName          String,
    monthShortName     String,
    weekOfYear         UInt8,
    dayOfWeek          UInt8,
    dayOfMonth         UInt8,
    dayOfYear          UInt16,
    dayName            String,
    dayShortName       String,
    fiscalYear         UInt16,
    fiscalQuarter      UInt8,
    fiscalMonth        UInt8,
    isWeekend          UInt8,
    isHoliday          UInt8,
    isBusinessDay      UInt8,
    isLastDayOfMonth   UInt8,
    daysInMonth        UInt8,
    yearMonthNumber    UInt32,
    yearQuarterNumber  UInt32,
    previousDayDateKey UInt32,
    nextDayDateKey     UInt32
)
    engine = MergeTree PRIMARY KEY (orgchartCode, projectCode, employeeCode, dateKey, customerName)
        ORDER BY (orgchartCode, projectCode, employeeCode, dateKey, customerName)
        SETTINGS index_granularity = 8192;

create table ZCRMR018
(
    customerCode       String,
    propertyUnitCode   String,
    propertyIdentity   Nullable(String),
    customerName       String,
    identityCode       String,
    customerPhone      String,
    customerAddress    String,
    countTicketCancel  UInt64,
    amountDeposit      Int64,
    orgchartName       String,
    note               String,
    orgchartCode       String,
    projectCode        String,
    employeeCode       String,
    dateKey            UInt32,
    fullDate           Date,
    dateString         String,
    year               UInt16,
    quarter            UInt8,
    quarterName        String,
    month              UInt8,
    monthName          String,
    monthShortName     String,
    weekOfYear         UInt8,
    dayOfWeek          UInt8,
    dayOfMonth         UInt8,
    dayOfYear          UInt16,
    dayName            String,
    dayShortName       String,
    fiscalYear         UInt16,
    fiscalQuarter      UInt8,
    fiscalMonth        UInt8,
    isWeekend          UInt8,
    isHoliday          UInt8,
    isBusinessDay      UInt8,
    isLastDayOfMonth   UInt8,
    daysInMonth        UInt8,
    yearMonthNumber    UInt32,
    yearQuarterNumber  UInt32,
    previousDayDateKey UInt32,
    nextDayDateKey     UInt32
)
    engine = MergeTree PRIMARY KEY (orgchartCode, projectCode, employeeCode, dateKey, customerName)
        ORDER BY (orgchartCode, projectCode, employeeCode, dateKey, customerName)
        SETTINGS index_granularity = 8192;

create table ZCRMR019
(
    orgchartCode       String,
    projectCode        String,
    employeeCode       String,
    propertyIdentity   Nullable(String),
    customerName       String,
    identityCode       String,
    customerPhone      String,
    customerAddress    String,
    countTicket        UInt8,
    amountRefund       Int128,
    orgchartName       String,
    note               String,
    dateKey            UInt32,
    fullDate           Date,
    dateString         String,
    year               UInt16,
    quarter            UInt8,
    quarterName        String,
    month              UInt8,
    monthName          String,
    monthShortName     String,
    weekOfYear         UInt8,
    dayOfWeek          UInt8,
    dayOfMonth         UInt8,
    dayOfYear          UInt16,
    dayName            String,
    dayShortName       String,
    fiscalYear         UInt16,
    fiscalQuarter      UInt8,
    fiscalMonth        UInt8,
    isWeekend          UInt8,
    isHoliday          UInt8,
    isBusinessDay      UInt8,
    isLastDayOfMonth   UInt8,
    daysInMonth        UInt8,
    yearMonthNumber    UInt32,
    yearQuarterNumber  UInt32,
    previousDayDateKey UInt32,
    nextDayDateKey     UInt32
)
    engine = MergeTree PRIMARY KEY (projectCode, orgchartCode, employeeCode, dateKey)
        ORDER BY (projectCode, orgchartCode, employeeCode, dateKey)
        SETTINGS index_granularity = 8192;

create table ZCRMR063
(
    orgchartCode         String,
    orgchartName         String,
    employeeCode         String,
    projectCode          String,
    projectName          String,
    propertyUnitIdentity String,
    propertyCode         Nullable(String),
    floor                Nullable(Int32),
    bedroom              Nullable(Int32),
    direction            Nullable(String),
    view                 Nullable(String),
    insideArea           Nullable(Float32),
    area                 Nullable(Float32),
    transactionCode      String,
    statusCode           String,
    statusName           String,
    statusDate           Nullable(String),
    note                 String,
    customerCode         String,
    customerName         String,
    customerNameContract String,
    price                Nullable(Int128),
    priceVat             Nullable(Int128),
    landPrice            Nullable(Int128),
    priceAbove           Nullable(Int128),
    priceAboveVat        Nullable(Int128),
    paymentMethod        String,
    valueByPaymentPlan   Int128,
    discountValue        Int8,
    discountDescription  String,
    finalAmount          Int128,
    vatAmount            Int128,
    finalAmountVat       Int128,
    landUseFee           Int128,
    maintenanceFee       Nullable(Int32),
    totalAmount          Nullable(Int64),
    employeeName         String,
    depositDate          DateTime,
    depositAmount        Int128,
    totalAmountPayment   Nullable(Int128),
    depositCancelDate    Nullable(DateTime),
    contractCancelDate   Nullable(DateTime),
    dateKey              UInt32,
    fullDate             Date,
    dateString           String,
    year                 UInt16,
    quarter              UInt8,
    quarterName          String,
    month                UInt8,
    monthName            String,
    monthShortName       String,
    weekOfYear           UInt8,
    dayOfWeek            UInt8,
    dayOfMonth           UInt8,
    dayOfYear            UInt16,
    dayName              String
)
    engine = MergeTree PRIMARY KEY (propertyUnitIdentity, orgchartCode, projectName, customerName, depositAmount,
                                    dateKey)
        ORDER BY (propertyUnitIdentity, orgchartCode, projectName, customerName, depositAmount, dateKey)
        SETTINGS index_granularity = 8192;

create table ZCRMR072
(
    orgchartCode         String,
    employeeCode         String,
    projectCode          String,
    propertyUnitIdentity String,
    customerName         String,
    customerPhone        String,
    customerAddress      String,
    depositDate          DateTime,
    amountRefund         Int128,
    cancelReason         String,
    confirmEmployee      String,
    employeeName         String,
    dateKey              UInt32,
    fullDate             Date,
    dateString           String,
    year                 UInt16,
    quarter              UInt8,
    quarterName          String,
    month                UInt8,
    monthName            String,
    monthShortName       String,
    weekOfYear           UInt8,
    dayOfWeek            UInt8,
    dayOfMonth           UInt8,
    dayOfYear            UInt16,
    dayName              String,
    dayShortName         String,
    fiscalYear           UInt16,
    fiscalQuarter        UInt8,
    fiscalMonth          UInt8,
    isWeekend            UInt8,
    isHoliday            UInt8,
    isBusinessDay        UInt8,
    isLastDayOfMonth     UInt8,
    daysInMonth          UInt8,
    yearMonthNumber      UInt32,
    yearQuarterNumber    UInt32,
    previousDayDateKey   UInt32,
    nextDayDateKey       UInt32
)
    engine = MergeTree PRIMARY KEY (orgchartCode, customerName, customerPhone, depositDate, amountRefund, dateKey)
        ORDER BY (orgchartCode, customerName, customerPhone, depositDate, amountRefund, dateKey)
        SETTINGS index_granularity = 8192;

create table ZCRMR073
(
    orgchartCode         String,
    orgchartName         String,
    employeeCode         String,
    projectCode          String,
    projectName          String,
    propertyUnitIdentity String,
    customerName         String,
    customerAddress      String,
    countTicket          UInt8,
    amountRefund         Int128,
    depositDate          DateTime,
    note                 String,
    dateKey              UInt32,
    fullDate             Date,
    dateString           String,
    year                 UInt16,
    quarter              UInt8,
    quarterName          String,
    month                UInt8,
    monthName            String,
    monthShortName       String,
    weekOfYear           UInt8,
    dayOfWeek            UInt8,
    dayOfMonth           UInt8,
    dayOfYear            UInt16,
    dayName              String,
    dayShortName         String,
    fiscalYear           UInt16,
    fiscalQuarter        UInt8,
    fiscalMonth          UInt8,
    isWeekend            UInt8,
    isHoliday            UInt8,
    isBusinessDay        UInt8,
    isLastDayOfMonth     UInt8,
    daysInMonth          UInt8,
    yearMonthNumber      UInt32,
    yearQuarterNumber    UInt32,
    previousDayDateKey   UInt32,
    nextDayDateKey       UInt32
)
    engine = MergeTree PRIMARY KEY (orgchartCode, customerName, depositDate, amountRefund, projectName, dateKey)
        ORDER BY (orgchartCode, customerName, depositDate, amountRefund, projectName, dateKey)
        SETTINGS index_granularity = 8192;

create table ZCRMR074
(
    orgchartCode         String,
    orgchartName         String,
    employeeCode         String,
    projectCode          String,
    projectName          String,
    propertyUnitIdentity String,
    customerName         String,
    customerAddress      String,
    customerPhone        String,
    depositDate          DateTime,
    amountDeposit        Int128,
    cancelReason         String,
    posConfirm           String,
    employeeConfirm      String,
    dateKey              UInt32,
    fullDate             Date,
    dateString           String,
    year                 UInt16,
    quarter              UInt8,
    quarterName          String,
    month                UInt8,
    monthName            String,
    monthShortName       String,
    weekOfYear           UInt8,
    dayOfWeek            UInt8,
    dayOfMonth           UInt8,
    dayOfYear            UInt16,
    dayName              String,
    dayShortName         String,
    fiscalYear           UInt16,
    fiscalQuarter        UInt8,
    fiscalMonth          UInt8,
    isWeekend            UInt8,
    isHoliday            UInt8,
    isBusinessDay        UInt8,
    isLastDayOfMonth     UInt8,
    daysInMonth          UInt8,
    yearMonthNumber      UInt32,
    yearQuarterNumber    UInt32,
    previousDayDateKey   UInt32,
    nextDayDateKey       UInt32
)
    engine = MergeTree PRIMARY KEY (orgchartCode, customerName, depositDate, amountDeposit, projectName, dateKey)
        ORDER BY (orgchartCode, customerName, depositDate, amountDeposit, projectName, dateKey)
        SETTINGS index_granularity = 8192;

create table ZCRMR075
(
    orgchartCode         String,
    orgchartName         String,
    employeeCode         String,
    projectCode          String,
    projectName          String,
    propertyUnitIdentity String,
    customerName         String,
    customerAddress      String,
    customerPhone        String,
    depositDate          DateTime,
    amountDeposit        Int128,
    cancelReason         String,
    note                 String,
    dateKey              UInt32,
    fullDate             Date,
    dateString           String,
    year                 UInt16,
    quarter              UInt8,
    quarterName          String,
    month                UInt8,
    monthName            String,
    monthShortName       String,
    weekOfYear           UInt8,
    dayOfWeek            UInt8,
    dayOfMonth           UInt8,
    dayOfYear            UInt16,
    dayName              String,
    dayShortName         String,
    fiscalYear           UInt16,
    fiscalQuarter        UInt8,
    fiscalMonth          UInt8,
    isWeekend            UInt8,
    isHoliday            UInt8,
    isBusinessDay        UInt8,
    isLastDayOfMonth     UInt8,
    daysInMonth          UInt8,
    yearMonthNumber      UInt32,
    yearQuarterNumber    UInt32,
    previousDayDateKey   UInt32,
    nextDayDateKey       UInt32
)
    engine = MergeTree PRIMARY KEY (orgchartCode, customerName, depositDate, amountDeposit, projectName, dateKey)
        ORDER BY (orgchartCode, customerName, depositDate, amountDeposit, projectName, dateKey)
        SETTINGS index_granularity = 8192;

create table ZCRMR076
(
    orgchartCode         String,
    orgchartName         String,
    employeeCode         String,
    projectCode          String,
    projectName          String,
    propertyUnitIdentity String,
    customerName         String,
    customerAddress      String,
    customerPhone        String,
    signedDate           DateTime,
    amountDeposit        Int128,
    cancelReason         String,
    posConfirm           String,
    employeeConfirm      String,
    dateKey              UInt32,
    fullDate             Date,
    dateString           String,
    year                 UInt16,
    quarter              UInt8,
    quarterName          String,
    month                UInt8,
    monthName            String,
    monthShortName       String,
    weekOfYear           UInt8,
    dayOfWeek            UInt8,
    dayOfMonth           UInt8,
    dayOfYear            UInt16,
    dayName              String,
    dayShortName         String,
    fiscalYear           UInt16,
    fiscalQuarter        UInt8,
    fiscalMonth          UInt8,
    isWeekend            UInt8,
    isHoliday            UInt8,
    isBusinessDay        UInt8,
    isLastDayOfMonth     UInt8,
    daysInMonth          UInt8,
    yearMonthNumber      UInt32,
    yearQuarterNumber    UInt32,
    previousDayDateKey   UInt32,
    nextDayDateKey       UInt32
)
    engine = MergeTree PRIMARY KEY (propertyUnitIdentity, orgchartCode, projectName, customerName, amountDeposit,
                                    dateKey)
        ORDER BY (propertyUnitIdentity, orgchartCode, projectName, customerName, amountDeposit, dateKey)
        SETTINGS index_granularity = 8192;

create table ZCRMR077
(
    orgchartCode         String,
    orgchartName         String,
    employeeCode         String,
    projectCode          String,
    projectName          String,
    propertyUnitIdentity String,
    customerName         String,
    customerAddress      String,
    customerPhone        String,
    signedDate           DateTime,
    amountDeposit        Int128,
    cancelReason         String,
    posConfirm           String,
    employeeConfirm      String,
    dateKey              UInt32,
    fullDate             Date,
    dateString           String,
    year                 UInt16,
    quarter              UInt8,
    quarterName          String,
    month                UInt8,
    monthName            String,
    monthShortName       String,
    weekOfYear           UInt8,
    dayOfWeek            UInt8,
    dayOfMonth           UInt8,
    dayOfYear            UInt16,
    dayName              String,
    dayShortName         String,
    fiscalYear           UInt16,
    fiscalQuarter        UInt8,
    fiscalMonth          UInt8,
    isWeekend            UInt8,
    isHoliday            UInt8,
    isBusinessDay        UInt8,
    isLastDayOfMonth     UInt8,
    daysInMonth          UInt8,
    yearMonthNumber      UInt32,
    yearQuarterNumber    UInt32,
    previousDayDateKey   UInt32,
    nextDayDateKey       UInt32
)
    engine = MergeTree PRIMARY KEY (propertyUnitIdentity, orgchartCode, projectName, customerName, amountDeposit,
                                    dateKey)
        ORDER BY (propertyUnitIdentity, orgchartCode, projectName, customerName, amountDeposit, dateKey)
        SETTINGS index_granularity = 8192;

create table accounts
(
    _id               String,
    operationType     String,
    clusterTime       String,
    wallTime          String,
    fullDocument      String,
    ns                String,
    documentKey       String,
    updateDescription String,
    id                String materialized JSONExtract(documentKey, '_id', 'String'),
    valid_from        DateTime default now()
)
    engine = MergeTree PRIMARY KEY (id, valid_from)
        ORDER BY (id, valid_from)
        SETTINGS index_granularity = 8192;

create table commission_lists
(
    _id               String,
    operationType     String,
    clusterTime       String,
    wallTime          String,
    fullDocument      String,
    ns                String,
    documentKey       String,
    updateDescription String,
    period            String materialized JSONExtract(fullDocument, 'period', 'String'),
    code              String materialized JSONExtract(fullDocument, 'code', 'String'),
    name              String materialized JSONExtract(fullDocument, 'name', 'String'),
    id                String materialized JSONExtract(documentKey, '_id', 'String'),
    type              String materialized JSONExtract(fullDocument, 'type', 'String'),
    status            String materialized JSONExtract(fullDocument, 'status', 'String'),
    active            String materialized JSONExtract(fullDocument, 'active', 'String'),
    pos               String materialized JSONExtract(fullDocument, 'pos', 'String'),
    pos_id            String materialized JSONExtract(pos, 'id', 'String'),
    ts                DateTime materialized JSONExtract(clusterTime, 't', 'DateTime'),
    timestamp         String materialized JSONExtract(clusterTime, '$timestamp', 'String'),
    valid_from        DateTime           default now(),
    created_date      Nullable(DateTime) default JSONExtract(fullDocument, 'createdDate', 'DateTime')
)
    engine = MergeTree PRIMARY KEY _id
        ORDER BY _id
        SETTINGS index_granularity = 8192;

create table commission_policies
(
    _id               String,
    operationType     String,
    clusterTime       String,
    wallTime          String,
    fullDocument      String,
    ns                String,
    documentKey       String,
    updateDescription String,
    active            String materialized JSONExtract(fullDocument, 'active', 'String'),
    code              String materialized JSONExtract(fullDocument, 'code', 'String'),
    name              String materialized JSONExtract(fullDocument, 'name', 'String'),
    id                String materialized JSONExtract(documentKey, '_id', 'String'),
    is_publish        String materialized JSONExtract(fullDocument, 'isPublish', 'String'),
    start_date        DateTime materialized JSONExtract(fullDocument, 'startDate', 'DateTime'),
    end_date          DateTime materialized JSONExtract(fullDocument, 'endDate', 'DateTime'),
    policy_type       String materialized JSONExtract(fullDocument, 'policyType', 'String'),
    is_progressive    String materialized JSONExtract(fullDocument, 'isProgressive', 'String'),
    is_vat            String materialized JSONExtract(fullDocument, 'isVAT', 'String'),
    ts                DateTime materialized JSONExtract(clusterTime, 't', 'DateTime'),
    timestamp         String materialized JSONExtract(clusterTime, '$timestamp', 'String'),
    valid_from        DateTime           default now(),
    created_date      Nullable(DateTime) default JSONExtract(fullDocument, 'createdDate', 'DateTime')
)
    engine = MergeTree PRIMARY KEY _id
        ORDER BY _id
        SETTINGS index_granularity = 8192;

create table commission_records
(
    _id                           String,
    operationType                 String,
    clusterTime                   String,
    wallTime                      String,
    fullDocument                  String,
    ns                            String,
    documentKey                   String,
    updateDescription             String,
    id                            String materialized JSONExtract(documentKey, 'id', 'String'),
    code                          String materialized JSONExtract(fullDocument, 'code', 'String'),
    escrow_date                   DateTime materialized fromUnixTimestamp64Milli(JSONExtract(fullDocument, 'escrowDate', '$date', 'Int64')),
    submit_date                   DateTime materialized fromUnixTimestamp64Milli(JSONExtract(fullDocument, 'submitDate', '$date', 'Int64')),
    transaction_phase             Int32 materialized JSONExtract(fullDocument, 'transactionPhase', 'Int32'),
    transaction_price             Int64 materialized JSONExtract(fullDocument, 'transactionPrice', 'Int64'),
    vat_rate                      Int32 materialized JSONExtract(fullDocument, 'vatRate', 'Int32'),
    commission_revenue            Int64 materialized JSONExtract(fullDocument, 'commissionRevenue', 'Int64'),
    commission_received           Int64 materialized JSONExtract(fullDocument, 'commissionReceived', 'Int64'),
    revenue_rate                  Int32 materialized JSONExtract(fullDocument, 'revenue', 'rate', 'Int32'),
    revenue_received_rate         Int32 materialized JSONExtract(fullDocument, 'revenue', 'receivedRate', 'Int32'),
    source                        String materialized JSONExtract(fullDocument, 'source', 'String'),
    is_publish                    Bool materialized JSONExtract(fullDocument, 'isPublish', 'Boolean'),
    commission_id                 String materialized JSONExtract(fullDocument, 'commission', 'id', 'String'),
    project_id                    String materialized JSONExtract(fullDocument, 'project', 'id', 'String'),
    sale_policy_id                String materialized JSONExtract(fullDocument, 'salePolicy', 'id', 'String'),
    commission_policy_personal_id String materialized JSONExtract(fullDocument, 'commissionPolicyPersonal', 'id', 'String'),
    commission_policy_manager_id  String materialized JSONExtract(fullDocument, 'commissionPolicyManager', 'id', 'String'),
    employee_id                   String materialized JSONExtract(fullDocument, 'employees', 'id', 'String'),
    created_date                  DateTime materialized fromUnixTimestamp64Milli(JSONExtract(fullDocument, 'createdDate', '$date', 'Int64')),
    modified_date                 DateTime materialized fromUnixTimestamp64Milli(JSONExtract(fullDocument, 'modifiedDate', '$date', 'Int64')),
    sync_time                     DateTime default now(),
    customer_name                 String materialized JSONExtract(fullDocument, 'customer', 'name', 'String'),
    date_key                      UInt32   default toYYYYMMDD(now())
)
    engine = MergeTree PRIMARY KEY _id
        ORDER BY _id
        SETTINGS index_granularity = 8192;

create table commissions
(
    _id               String,
    operationType     String,
    clusterTime       String,
    wallTime          String,
    fullDocument      String,
    ns                String,
    documentKey       String,
    updateDescription String,
    id                String materialized JSONExtract(documentKey, '_id', 'String'),
    valid_from        DateTime default now()
)
    engine = MergeTree PRIMARY KEY (id, valid_from)
        ORDER BY (id, valid_from)
        SETTINGS index_granularity = 8192;

create table contracts
(
    _id               String,
    operationType     String,
    clusterTime       String,
    wallTime          String,
    fullDocument      String,
    ns                String,
    documentKey       String,
    updateDescription String,
    id                String materialized JSONExtract(documentKey, '_id', 'String'),
    valid_from        DateTime default now()
)
    engine = MergeTree PRIMARY KEY (id, valid_from)
        ORDER BY (id, valid_from)
        SETTINGS index_granularity = 8192;

create table customers
(
    _id               String,
    operationType     String,
    clusterTime       String,
    wallTime          String,
    fullDocument      String,
    ns                String,
    documentKey       String,
    updateDescription String,
    id                String materialized JSONExtract(documentKey, '_id', 'String'),
    valid_from        DateTime default now()
)
    engine = MergeTree PRIMARY KEY (id, valid_from)
        ORDER BY (id, valid_from)
        SETTINGS index_granularity = 8192;

create table demand_customers
(
    _id               String,
    operationType     String,
    clusterTime       String,
    wallTime          String,
    fullDocument      String,
    ns                String,
    documentKey       String,
    updateDescription String,
    code              String materialized JSONExtract(fullDocument, 'code', 'String'),
    id                Nullable(String) default JSONExtract(documentKey, '_id', 'String'),
    type              String materialized JSONExtract(fullDocument, 'type', 'String'),
    tax_code          String materialized JSONExtract(fullDocument, 'taxCode', 'String'),
    personal_info     String materialized JSONExtract(fullDocument, 'personalInfo', 'String'),
    info              String materialized JSONExtract(fullDocument, 'info', 'String'),
    pos               String materialized JSONExtract(fullDocument, 'pos', 'String'),
    employee          String materialized JSONExtract(fullDocument, 'employee', 'String'),
    employee_id       String materialized JSONExtract(employee, 'id', 'String'),
    pos_id            String materialized JSONExtract(pos, 'id', 'String'),
    company           String materialized JSONExtract(fullDocument, 'company', 'String'),
    name              String materialized JSONExtract(personal_info, 'name', 'String'),
    customer_birthday String materialized JSONExtract(info, 'birthday', 'String'),
    phone             String materialized JSONExtract(personal_info, 'phone', 'String'),
    email             String materialized JSONExtract(personal_info, 'email', 'String'),
    identities        String materialized JSONExtract(personal_info, 'identities', 'String'),
    identity_code     String materialized simpleJSONExtractInt(identities, 'value'),
    company_name      String materialized JSONExtract(company, 'name', 'String'),
    issue_date        String materialized JSONExtract(company, 'issueDate', 'String'),
    issue_location    String materialized JSONExtract(company, 'issueLocation', 'String'),
    ts                DateTime materialized JSONExtract(clusterTime, 't', 'DateTime'),
    timestamp         String materialized JSONExtract(clusterTime, '$timestamp', 'String'),
    valid_from        DateTime         default now(),
    identity_date     String materialized simpleJSONExtractInt(identities, 'date'),
    identity_place    String materialized simpleJSONExtractInt(identities, 'place'),
    address           String materialized JSONExtract(info, 'address', 'String'),
    full_address      String materialized JSONExtract(info, 'fullAddress', 'String')
)
    engine = MergeTree PRIMARY KEY _id
        ORDER BY _id
        SETTINGS index_granularity = 8192;

create table dim_accounts
(
    id         String,
    username   String,
    is_admin   String,
    status     String,
    code       String,
    refer_code String,
    fullname   String,
    email      String,
    org_code   String,
    org_name   String,
    position   String,
    valid_from DateTime,
    is_current UInt8,
    valid_to   DateTime
)
    engine = MergeTree PRIMARY KEY (id, username, code, org_code)
        ORDER BY (id, username, code, org_code)
        SETTINGS index_granularity = 8192;

create table dim_commissions
(
    id          String,
    name        String,
    period_from String,
    period_to   String,
    period      String,
    type        String,
    year        String,
    status      String,
    pos_id      String,
    is_active   String,
    sale_policy String,
    code        String,
    valid_from  DateTime,
    is_current  UInt8,
    valid_to    DateTime
)
    engine = MergeTree PRIMARY KEY (id, code, is_current)
        ORDER BY (id, code, is_current)
        SETTINGS index_granularity = 8192;

create table dim_contracts
(
    id              String,
    name            String,
    code            String,
    type            String,
    signed_date     String,
    status          String,
    valid_from      DateTime,
    is_current      UInt8,
    valid_to        DateTime,
    payment_percent Nullable(Int32),
    update_date     Nullable(String),
    maintenance_fee Nullable(Int32),
    handover_status Nullable(String)
)
    engine = MergeTree PRIMARY KEY id
        ORDER BY id
        SETTINGS index_granularity = 8192;

create table dim_customers
(
    name           String,
    code           String,
    id             String,
    active         String,
    state          String,
    type           String,
    gender         String,
    birthday       String,
    identity_code  String,
    identity_place String,
    identity_date  String,
    full_address   String,
    phone          String,
    email          String,
    company_name   String,
    issue_date     String,
    issue_location String,
    pos_id         String,
    employee_id    String,
    valid_from     DateTime,
    is_current     UInt8,
    valid_to       DateTime,
    tax_code       Nullable(String)
)
    engine = MergeTree PRIMARY KEY (id, code, is_current)
        ORDER BY (id, code, is_current)
        SETTINGS index_granularity = 8192;

create table dim_date
(
    date_key              UInt32,
    full_date             Date,
    date_string           String,
    year                  UInt16,
    quarter               UInt8,
    quarter_name          String,
    month                 UInt8,
    month_name            String,
    month_short_name      String,
    week_of_year          UInt8,
    day_of_week           UInt8,
    day_of_month          UInt8,
    day_of_year           UInt16,
    day_name              String,
    day_short_name        String,
    fiscal_year           UInt16,
    fiscal_quarter        UInt8,
    fiscal_month          UInt8,
    is_weekend            UInt8,
    is_holiday            UInt8,
    is_business_day       UInt8,
    is_last_day_of_month  UInt8,
    days_in_month         UInt8,
    year_month_number     UInt32,
    year_quarter_number   UInt32,
    previous_day_date_key UInt32,
    next_day_date_key     UInt32
)
    engine = MergeTree ORDER BY date_key
        SETTINGS index_granularity = 8192;

create table dim_demand_customers
(
    name              String,
    code              String,
    id                String,
    type              String,
    customer_birthday String,
    identity_code     String,
    identity_place    String,
    identity_date     String,
    full_address      String,
    phone             String,
    email             String,
    company_name      String,
    issue_date        String,
    issue_location    String,
    pos_id            String,
    employee_id       String,
    valid_from        DateTime,
    tax_code          String,
    is_current        UInt8,
    valid_to          DateTime
)
    engine = MergeTree PRIMARY KEY (id, code, is_current)
        ORDER BY (id, code, is_current)
        SETTINGS index_granularity = 8192;

create table dim_employees
(
    name             String,
    id               String,
    code             String,
    identity_code    String,
    identity_date    String,
    identity_address String,
    phone            String,
    email            String,
    is_line_manager  String,
    tax_id           String,
    pos_id           String,
    valid_from       DateTime,
    is_current       UInt8,
    valid_to         DateTime
)
    engine = MergeTree PRIMARY KEY (id, code, is_current)
        ORDER BY (id, code, is_current)
        SETTINGS index_granularity = 8192;

create table dim_orgcharts
(
    name         String,
    short_name   String,
    name_vn      String,
    name_en      String,
    level        String,
    code         String,
    logo         String,
    type         String,
    id           String,
    parent_code  String,
    parent_id    String,
    parent_name  String,
    status       String,
    phone        String,
    active       String,
    tax_number   String,
    soft_delete  String,
    created_date String,
    valid_from   DateTime,
    is_current   UInt8,
    valid_to     DateTime
)
    engine = MergeTree PRIMARY KEY (id, code, is_current, status)
        ORDER BY (id, code, is_current, status)
        SETTINGS index_granularity = 8192;

create table dim_projects
(
    name        String,
    name_public String,
    id          String,
    code        String,
    type        String,
    active      String,
    address     String,
    valid_from  DateTime,
    is_current  UInt8,
    valid_to    DateTime
)
    engine = MergeTree PRIMARY KEY (id, code, is_current)
        ORDER BY (id, code, is_current)
        SETTINGS index_granularity = 8192;

create table dim_property_units
(
    id                String,
    code              Nullable(String),
    active            Nullable(String),
    price             Nullable(Int128),
    price_vat         Nullable(Int128),
    is_completed      Nullable(Int8),
    project_id        Nullable(String),
    short_code        Nullable(String),
    contract_id       Nullable(String),
    valid_from        DateTime,
    is_current        UInt8,
    valid_to          Nullable(DateTime),
    property_identity String,
    floor             Nullable(Int32),
    bedroom           Nullable(Int32),
    inside_area       Nullable(Float32),
    area              Nullable(Float32),
    land_price        Nullable(Int128),
    price_above       Nullable(Int128),
    price_above_vat   Nullable(Int128),
    house_price_vat   Nullable(Int128),
    house_price       Nullable(Int128),
    direction         Nullable(String),
    view              Nullable(String)
)
    engine = MergeTree PRIMARY KEY id
        ORDER BY id
        SETTINGS index_granularity = 8192;

create table dim_time
(
    time_key         UInt32,
    full_time        String,
    hour_24          UInt8,
    hour_12          UInt8,
    minute           UInt8,
    second           UInt8,
    am_pm            String,
    time_in_12hour   String,
    time_in_24hour   String,
    time_of_day_name String,
    is_business_hour UInt8,
    shift_name       String,
    is_peak_hour     UInt8,
    hour_minute      UInt16,
    minute_of_day    UInt16,
    second_of_day    UInt32
)
    engine = MergeTree ORDER BY time_key
        SETTINGS index_granularity = 8192;

create table dim_users
(
    code String,
    name String,
    age  Int32
)
    engine = MergeTree PRIMARY KEY code
        ORDER BY code
        SETTINGS index_granularity = 8192;

create table employees
(
    _id               String,
    operationType     String,
    clusterTime       String,
    wallTime          String,
    fullDocument      String,
    ns                String,
    documentKey       String,
    updateDescription String,
    pos               String materialized JSONExtract(fullDocument, 'pos', 'String'),
    code              String materialized JSONExtract(fullDocument, 'code', 'String'),
    name              String materialized JSONExtract(fullDocument, 'name', 'String'),
    id                String materialized JSONExtract(documentKey, '_id', 'String'),
    pos_id            String materialized JSONExtract(pos, 'id', 'String'),
    ts                DateTime materialized JSONExtract(clusterTime, 't', 'DateTime'),
    timestamp         String materialized JSONExtract(clusterTime, '$timestamp', 'String'),
    valid_from        DateTime default now(),
    tax_id            String materialized JSONExtract(fullDocument, 'taxId', 'String'),
    identity_code     String materialized JSONExtract(fullDocument, 'identityCode', 'String'),
    identity_date     String materialized JSONExtract(fullDocument, 'identityDate', 'String'),
    identity_address  String materialized JSONExtract(fullDocument, 'identityAddress', 'String')
)
    engine = MergeTree PRIMARY KEY _id
        ORDER BY _id
        SETTINGS index_granularity = 8192;

create table expense_lists
(
    _id               String,
    operationType     String,
    clusterTime       String,
    wallTime          String,
    fullDocument      String,
    ns                String,
    documentKey       String,
    updateDescription String,
    id                String materialized JSONExtract(documentKey, '_id', 'String'),
    valid_from        DateTime default now()
)
    engine = MergeTree PRIMARY KEY (id, valid_from)
        ORDER BY (id, valid_from)
        SETTINGS index_granularity = 8192;

create table expense_records
(
    _id                        String,
    operationType              String,
    clusterTime                String,
    wallTime                   String,
    fullDocument               String,
    ns                         String,
    documentKey                String,
    updateDescription          String,
    timestamp                  String materialized JSONExtract(clusterTime, '$timestamp', 'String'),
    id                         String materialized JSONExtract(documentKey, '_id', 'String'),
    code                       String materialized JSONExtract(fullDocument, 'code', 'String'),
    escrow_date                String materialized JSONExtract(fullDocument, 'escrowDate', 'DateTime'),
    submit_date                DateTime materialized JSONExtract(fullDocument, 'submitDate', 'DateTime'),
    revenue                    String materialized JSONExtract(fullDocument, 'revenue', 'String'),
    revenue_rate               Float64 materialized JSONExtract(revenue, 'rate', 'Float64'),
    revenue_received_rate      Float64 materialized JSONExtract(revenue, 'receivedRate', 'Float64'),
    note                       String materialized JSONExtract(fullDocument, 'note', 'String'),
    is_publish                 String materialized JSONExtract(fullDocument, 'isPublish', 'String'),
    transaction                String materialized JSONExtract(fullDocument, 'transaction', 'String'),
    transaction_state          String materialized JSONExtract(transaction, 'state', 'String'),
    transaction_consulting_fee UInt64 materialized JSONExtract(transaction, 'consultingFee', 'UInt64'),
    transaction_other_fee      UInt64 materialized JSONExtract(transaction, 'otherFee', 'UInt64'),
    transaction_register_fee   UInt64 materialized JSONExtract(transaction, 'registerFee', 'UInt64'),
    transaction_total          UInt64 materialized JSONExtract(transaction, 'total', 'UInt64'),
    transaction_advance        UInt64 materialized JSONExtract(transaction, 'advance', 'UInt64'),
    transaction_residual       UInt64 materialized JSONExtract(transaction, 'residual', 'UInt64'),
    transaction_received       UInt64 materialized JSONExtract(transaction, 'received', 'UInt64'),
    commission                 String materialized JSONExtract(fullDocument, 'commission', 'String'),
    commission_period_id       String materialized JSONExtract(commission, 'id', 'String'),
    sale_policy                String materialized JSONExtract(fullDocument, 'salePolicy', 'String'),
    sale_policy_id             String materialized JSONExtract(sale_policy, 'id', 'String'),
    project                    String materialized JSONExtract(fullDocument, 'project', 'String'),
    project_id                 String materialized JSONExtract(project, 'id', 'String'),
    customer                   String materialized JSONExtract(fullDocument, 'customer', 'String'),
    customer_name              String materialized JSONExtract(customer, 'name', 'String'),
    property_unit              String materialized JSONExtract(fullDocument, 'propertyUnit', 'String'),
    view1                      UInt64 materialized JSONExtract(property_unit, 'view1', 'UInt64'),
    vat_pricePrice             UInt64 materialized JSONExtract(property_unit, 'nvatPrice', 'UInt64'),
    price                      UInt64 materialized JSONExtract(property_unit, 'price', 'UInt64'),
    created_date               DateTime default now(),
    modified_date              DateTime default now(),
    valid_from                 DateTime materialized toDateTime(JSONExtractInt(timestamp, 't')),
    date_key                   UInt32   default toYYYYMMDD(now())
)
    engine = MergeTree PRIMARY KEY _id
        ORDER BY _id
        SETTINGS index_granularity = 8192;

create table fact_expense_records
(
    id                         String,
    code                       String,
    escrow_date                String,
    submit_date                String,
    revenue_rate               Float64 comment 'tỷ lệ phí',
    revenue_received_rate      Float64 comment 'tỷ lệ phí thực nhận',
    employee_revenue_rate      Float64 comment 'tỷ lệ chia',
    note                       String,
    is_publish                 String,
    created_date               String,
    modified_date              String,
    transaction_state          String comment 'trạng thái',
    transaction_consulting_fee UInt64 comment 'phí cơ bản',
    transaction_other_fee      UInt64 comment 'phí khác',
    transaction_register_fee   UInt64 comment 'phí đăng ký',
    transaction_total          UInt64 comment 'tổng sp đã tính phí',
    transaction_advance        UInt64 comment 'phí tạm ứng',
    transaction_residual       UInt64 comment 'phí còn lại',
    transaction_received       UInt64 comment 'phí đã nhận',
    commission_id              String,
    sale_policy_id             String,
    project_id                 String,
    customer_name              String,
    property_unit_identity     String comment 'mã sp',
    valid_from                 DateTime,
    is_current                 UInt8,
    date_key                   UInt32,
    price                      Nullable(UInt64) comment 'giá có vat',
    nvat_price                 UInt64 comment 'giá sp chưa vat'
)
    engine = MergeTree PRIMARY KEY (id, code, is_current, commission_id)
        ORDER BY (id, code, is_current, commission_id)
        SETTINGS index_granularity = 8192;

create table fact_orders
(
    id                           String,
    code                         String,
    note                         String,
    ticket_type                  String,
    status                       String,
    priority                     Int8,
    booking_ticket_code          String,
    escrow_ticket_code           String,
    amount                       Int128,
    created_date                 String,
    reciept_count                UInt64,
    total_amount_refund          Int128,
    total_amount_deposit         Int128,
    contract                     String,
    contract_id                  String,
    pos_id                       String,
    employee_id                  String,
    customer_code                String,
    project_id                   String,
    property_unit_id             String,
    date_key                     UInt32,
    valid_from                   DateTime,
    valid_to                     Nullable(DateTime),
    is_current                   Nullable(Int8),
    transfer_from_booking_ticket Nullable(Int128) comment 'số tiền chuyển từ YCDCH ',
    bank_account_number          Nullable(String),
    bank_name                    Nullable(String),
    amount_primary_contract      Nullable(Int128),
    modified_date                Nullable(String)
)
    engine = MergeTree PRIMARY KEY (id, status, ticket_type, date_key)
        ORDER BY (id, status, ticket_type, date_key)
        SETTINGS index_granularity = 8192;

create table fact_transactions
(
    id                      String,
    type                    String,
    code                    String,
    receipt_num             String,
    money                   UInt128,
    total_money             UInt128,
    transfered_money        UInt128,
    transfering_money       UInt128,
    contract_required_money UInt128,
    contract_receipt_money  UInt128,
    total_amount_print      UInt128,
    interest_amount         UInt128,
    state                   String,
    status                  String,
    contract_id             String,
    pos_id                  String,
    project_id              String,
    customer_code           String,
    consultant_id           String,
    property_unit_id        String,
    booking_ticket_code     String,
    ticket_note             String,
    bank_account_number     String,
    ticket_created_date     String comment 'ngày tạo ticket YCTV',
    bank_name               String,
    note                    String,
    created_date            String,
    timestamp               String,
    date_key                UInt32,
    valid_from              DateTime,
    valid_to                Nullable(DateTime),
    is_current              Nullable(Int8),
    ` escrow_ticket_code`   Nullable(String),
    ticket_type             Nullable(String)
)
    engine = MergeTree PRIMARY KEY (id, status, type, date_key)
        ORDER BY (id, status, type, date_key)
        SETTINGS index_granularity = 8192;

create table orgcharts
(
    _id               String,
    operationType     String,
    clusterTime       String,
    wallTime          String,
    fullDocument      String,
    ns                String,
    documentKey       String,
    updateDescription String,
    ts                DateTime materialized JSONExtract(clusterTime, 't', 'DateTime'),
    name              String materialized JSONExtract(fullDocument, 'name', 'String'),
    level             String materialized JSONExtract(fullDocument, 'level', 'Int32'),
    code              String materialized JSONExtract(fullDocument, 'code', 'String'),
    type              String materialized JSONExtract(fullDocument, 'type', 'String'),
    id                String materialized JSONExtract(documentKey, '_id', 'String'),
    parent_id         String materialized JSONExtract(fullDocument, 'parentId', 'String'),
    status            String materialized JSONExtract(fullDocument, 'status', 'String'),
    active            String materialized JSONExtract(fullDocument, 'active', 'String'),
    created_date      DateTime default now(),
    last_update       DateTime default now(),
    timestamp         String materialized JSONExtract(clusterTime, '$timestamp', 'String'),
    valid_from        DateTime default now()
)
    engine = MergeTree PRIMARY KEY _id
        ORDER BY _id
        SETTINGS index_granularity = 8192;

create table primary_transactions
(
    _id               String,
    operationType     String,
    clusterTime       String,
    wallTime          String,
    fullDocument      String,
    ns                String,
    documentKey       String,
    updateDescription String,
    id                String materialized JSONExtract(documentKey, '_id', 'String'),
    valid_from        DateTime default now()
)
    engine = MergeTree PRIMARY KEY (id, valid_from)
        ORDER BY (id, valid_from)
        SETTINGS index_granularity = 8192;

create table projects
(
    _id               String,
    operationType     String,
    clusterTime       String,
    wallTime          String,
    fullDocument      String,
    ns                String,
    documentKey       String,
    updateDescription String,
    code              String materialized JSONExtract(fullDocument, 'code', 'String'),
    name              String materialized JSONExtract(fullDocument, 'name', 'String'),
    id                String materialized JSONExtract(documentKey, '_id', 'String'),
    active            String materialized JSONExtract(fullDocument, 'active', 'String'),
    address           String materialized JSONExtract(fullDocument, 'address', 'String'),
    ts                DateTime materialized JSONExtract(clusterTime, 't', 'DateTime'),
    timestamp         String materialized JSONExtract(clusterTime, '$timestamp', 'String'),
    valid_from        DateTime         default now(),
    name_public       String materialized JSONExtract(fullDocument, 'namePublic', 'String'),
    type              Nullable(String) default JSONExtract(fullDocument, 'type', 'String')
)
    engine = MergeTree PRIMARY KEY _id
        ORDER BY _id
        SETTINGS index_granularity = 8192;

create table property_units
(
    _id               String,
    operationType     String,
    clusterTime       String,
    wallTime          String,
    fullDocument      String,
    ns                String,
    documentKey       String,
    updateDescription String,
    id                String materialized JSONExtract(documentKey, '_id', 'String'),
    valid_from        DateTime default now()
)
    engine = MergeTree PRIMARY KEY (id, valid_from)
        ORDER BY (id, valid_from)
        SETTINGS index_granularity = 8192;

create table sale_policies
(
    _id               String,
    operationType     String,
    clusterTime       String,
    wallTime          String,
    fullDocument      String,
    ns                String,
    documentKey       String,
    updateDescription String,
    active            String materialized JSONExtract(fullDocument, 'active', 'String'),
    code              String materialized JSONExtract(fullDocument, 'code', 'String'),
    name              String materialized JSONExtract(fullDocument, 'name', 'String'),
    id                String materialized JSONExtract(documentKey, '_id', 'String'),
    status            String materialized JSONExtract(fullDocument, 'status', 'String'),
    is_publish        String materialized JSONExtract(fullDocument, 'isPublish', 'String'),
    start_date        DateTime materialized JSONExtract(fullDocument, 'startDate', 'DateTime'),
    end_date          DateTime materialized JSONExtract(fullDocument, 'endDate', 'DateTime'),
    revenue_rate      Float64 materialized JSONExtract(fullDocument, 'revenueRate', 'Float64'),
    amount            Int32 materialized JSONExtract(fullDocument, 'amount', 'Int32'),
    comrate           Int32 materialized JSONExtract(fullDocument, 'comrate', 'Int32'),
    comamount         Int32 materialized JSONExtract(fullDocument, 'comamount', 'Int32'),
    comments          String materialized JSONExtract(fullDocument, 'comments', 'String'),
    ts                DateTime materialized JSONExtract(clusterTime, 't', 'DateTime'),
    timestamp         String materialized JSONExtract(clusterTime, '$timestamp', 'String'),
    valid_from        DateTime           default now(),
    created_date      Nullable(DateTime) default JSONExtract(fullDocument, 'createdDate', 'DateTime')
)
    engine = MergeTree PRIMARY KEY _id
        ORDER BY _id
        SETTINGS index_granularity = 8192;

create table transactions
(
    _id               String,
    operationType     String,
    clusterTime       String,
    wallTime          String,
    fullDocument      String,
    ns                String,
    documentKey       String,
    updateDescription String,
    id                String materialized JSONExtract(documentKey, '_id', 'String'),
    valid_from        DateTime default now(),
    date_key          UInt32   default toYYYYMMDD(now())
)
    engine = MergeTree PRIMARY KEY (id, valid_from, date_key)
        ORDER BY (id, valid_from, date_key)
        SETTINGS index_granularity = 8192;

CREATE VIEW crm_report.ZCRMR012
            (
             `propertyUnitCode` String,
             `projectName` String,
             `customerName` String,
             `escrowDate` DateTime,
             `submitDate` DateTime,
             `contractSignDate` String,
             `commissionPolicyCode` String,
             `employeeIdentityCode` String,
             `employeeIdentityAddress` String,
             `employeeIdentityDate` String,
             `employeeTaxId` String,
             `employeeCode` String,
             `commissionRevenue` Int64,
             `vatRate` Float64,
             `commissionReceived` Int64,
             `commissionDeducted` Int64,
             `employeeBankAccount` String,
             `employeeBankName` String,
             `commission_policy_personal_id` String
                )
AS
SELECT 'propertyUnitCode'                          AS propertyUnitCode,
       p.name                                      AS projectName,
       co.customer_name                            AS customerName,
       co.escrow_date                              AS escrowDate,
       co.submit_date                              AS submitDate,
       'contractSignDate'                          AS contractSignDate,
       'co.commissionPolicyPersonal.code'          AS commissionPolicyCode,
       e.identity_code                             AS employeeIdentityCode,
       e.identity_address                          AS employeeIdentityAddress,
       e.identity_date                             AS employeeIdentityDate,
       e.tax_id                                    AS employeeTaxId,
       e.code                                      AS employeeCode,
       co.commission_revenue                       AS commissionRevenue,
       co.vat_rate                                 AS vatRate,
       co.commission_received                      AS commissionReceived,
       commission_revenue - co.commission_received AS commissionDeducted,
       'e.bankAccount'                             AS employeeBankAccount,
       'e.bankName'                                AS employeeBankName,
       co.commission_policy_personal_id
FROM crm_report.fact_commission_records AS co
         LEFT JOIN crm_report.dim_employees AS e ON (co.employee_id = e.id) AND (e.is_current = 1)
         LEFT JOIN crm_report.dim_projects AS p ON (co.project_id = p.id) AND (p.is_current = 1)
         LEFT JOIN crm_report.dim_commission_policies AS cp
                   ON (co.commission_policy_personal_id = cp.id) AND (cp.is_current = 1);

CREATE VIEW crm_report.dim_commission_periods
            (
             `id` String,
             `name` String,
             `code` String,
             `period` String,
             `type` DateTime,
             `status` String,
             `active` String,
             `valid_from` DateTime,
             `is_current` UInt8,
             `valid_to` Nullable(DateTime)
                )
AS
SELECT id,
       name,
       code,
       period,
       type,
       status,
       active,
       valid_from,
       if(c.valid_from = latest.latest_valid_from, 1, 0)                            AS is_current,
       if(c.valid_from != latest.latest_valid_from, latest.latest_valid_from, NULL) AS valid_to
FROM crm_report.commission_periods AS c
         LEFT JOIN
     (
         SELECT id,
                max(valid_from) AS latest_valid_from
         FROM crm_report.contracts
         GROUP BY id
         ) AS latest ON c.id = latest.id;

CREATE VIEW crm_report.dim_commission_policies
            (
             `active` String,
             `code` String,
             `name` String,
             `id` String,
             `is_publish` String,
             `start_date` DateTime,
             `end_date` DateTime,
             `policy_type` String,
             `is_progressive` String,
             `is_vat` String,
             `ts` DateTime,
             `timestamp` String,
             `created_date` Nullable(DateTime),
             `valid_from` DateTime,
             `is_current` UInt8,
             `valid_to` Nullable(DateTime)
                )
AS
SELECT active,
       code,
       name,
       id,
       is_publish,
       start_date,
       end_date,
       policy_type,
       is_progressive,
       is_vat,
       ts,
       timestamp,
       created_date,
       valid_from,
       if(c.valid_from = latest.latest_valid_from, 1, 0)                            AS is_current,
       if(c.valid_from != latest.latest_valid_from, latest.latest_valid_from, NULL) AS valid_to
FROM crm_report.commission_policies AS c
         LEFT JOIN
     (
         SELECT id,
                max(valid_from) AS latest_valid_from
         FROM crm_report.contracts
         GROUP BY id
         ) AS latest ON c.id = latest.id;

CREATE VIEW crm_report.dim_sale_policies
            (
             `active` String,
             `code` String,
             `name` String,
             `id` String,
             `status` String,
             `is_publish` String,
             `start_date` DateTime,
             `end_date` DateTime,
             `revenue_rate` Float64,
             `amount` Int32,
             `comrate` Int32,
             `comamount` Int32,
             `comments` String,
             `ts` DateTime,
             `timestamp` String,
             `valid_from` DateTime,
             `is_current` UInt8,
             `valid_to` Nullable(DateTime)
                )
AS
SELECT active,
       code,
       name,
       id,
       status,
       is_publish,
       start_date,
       end_date,
       revenue_rate,
       amount,
       comrate,
       comamount,
       comments,
       ts,
       timestamp,
       valid_from,
       if(c.valid_from = latest.latest_valid_from, 1, 0)                            AS is_current,
       if(c.valid_from != latest.latest_valid_from, latest.latest_valid_from, NULL) AS valid_to
FROM crm_report.sale_policies AS c
         LEFT JOIN
     (
         SELECT id,
                max(valid_from) AS latest_valid_from
         FROM crm_report.contracts
         GROUP BY id
         ) AS latest ON c.id = latest.id;

CREATE VIEW crm_report.fact_commission_records
            (
             `code` String,
             `source` String,
             `escrow_date` DateTime,
             `submit_date` DateTime,
             `transaction_phase` Int64,
             `transaction_price` Int64,
             `vat_rate` Float64,
             `commission_revenue` Int64,
             `commission_received` Int64,
             `revenue_rate` Float64,
             `revenue_received_rate` Float64,
             `is_publish` UInt8,
             `created_date` DateTime,
             `modified_date` DateTime,
             `timestamp` UInt32,
             `month_key` Int64,
             `commission_id` String,
             `project_id` String,
             `sale_policy_id` String,
             `commission_policy_personal_id` String,
             `commission_policy_manager_id` String,
             `employee_id` String,
             `customer_name` String
                )
AS
SELECT JSONExtractString(fullDocument, 'code')                                    AS code,
       JSONExtractString(fullDocument, 'source')                                  AS source,
       parseDateTimeBestEffort(JSONExtractString(fullDocument, 'escrowDate'))     AS escrow_date,
       parseDateTimeBestEffort(JSONExtractString(fullDocument, 'submitDate'))     AS submit_date,
       JSONExtractInt(fullDocument, 'transactionPhase')                           AS transaction_phase,
       JSONExtractInt(fullDocument, 'transactionPrice')                           AS transaction_price,
       JSONExtractFloat(fullDocument, 'vatRate')                                  AS vat_rate,
       JSONExtractInt(fullDocument, 'commissionRevenue')                          AS commission_revenue,
       JSONExtractInt(fullDocument, 'commissionReceived')                         AS commission_received,
       JSONExtractFloat(fullDocument, 'revenue', 'rate')                          AS revenue_rate,
       JSONExtractFloat(fullDocument, 'revenue', 'receivedRate')                  AS revenue_received_rate,
       JSONExtractBool(fullDocument, 'isPublish')                                 AS is_publish,
       parseDateTimeBestEffort(JSONExtractString(fullDocument, 'createdDate'))    AS created_date,
       parseDateTimeBestEffort(JSONExtractString(fullDocument, 'modifiedDate'))   AS modified_date,
       toUnixTimestamp(created_date)                                              AS timestamp,
       JSONExtractInt(fullDocument, 'commission', 'period')                       AS month_key,
       JSONExtractString(fullDocument, 'commission', 'id')                        AS commission_id,
       JSONExtractString(fullDocument, 'project', 'id')                           AS project_id,
       JSONExtractString(fullDocument, 'salePolicy', 'id')                        AS sale_policy_id,
       JSONExtractString(fullDocument, 'commissionPolicyPersonal', 'id')          AS commission_policy_personal_id,
       JSONExtractString(fullDocument, 'commissionPolicyManager', 'id')           AS commission_policy_manager_id,
       JSONExtractString(JSONExtractArrayRaw(fullDocument, 'employees')[1], 'id') AS employee_id,
       customer_name
FROM crm_report.commission_records;

