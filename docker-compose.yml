version: '3'

services:
#  dagster-webserver:
#    build:
#      context: .
#      dockerfile: Dockerfile
#    ports:
#      - "3000:3000"
#    volumes:
#      - ./:/app
#      - dagster_home:/home/<USER>/dagster_home
#    environment:
#      - DAGSTER_HOME=/home/<USER>/dagster_home
#    depends_on:
#      - dagster-daemon

  dagster-daemon:
    build:
      context: .
      dockerfile: Dockerfile.daemon
    volumes:
      - ./:/app
      - dagster_home:/home/<USER>/dagster_home
    environment:
      - DAGSTER_HOME=/home/<USER>/dagster_home
volumes:
  dagster_home: