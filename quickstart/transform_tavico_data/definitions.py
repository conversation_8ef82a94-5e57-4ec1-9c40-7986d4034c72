import dagster as dg
from dagster import multiprocess_executor

from quickstart.transform_tavico_data.assets.account import process_tavico_accounts

materialize_all_assets_job = dg.define_asset_job(
    "materialize_all_tavico_assets",
    selection=[process_tavico_accounts]
)

daily_schedule = dg.ScheduleDefinition(
    job=materialize_all_assets_job,
    cron_schedule="*/1 * * * *"
)
# Combine assets and schedules in Definitions
def get_definitions():
    return dg.Definitions(
        assets=[process_tavico_accounts],
        executor=multiprocess_executor,
        jobs=[materialize_all_assets_job],
        schedules=[daily_schedule]
    )
