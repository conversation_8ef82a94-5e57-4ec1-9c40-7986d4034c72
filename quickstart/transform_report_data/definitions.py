import dagster as dg
from dagster import multiprocess_executor

from .assets.ZCRMR001 import process_ZCRMR001
from .assets.ZCRMR003 import process_ZCRMR003
from .assets.ZCRMR004 import process_ZCRMR004
from .assets.ZCRMR005 import process_ZCRMR005
from .assets.ZCRMR006 import process_ZCRMR006
from .assets.ZCRMR007 import process_ZCRMR007
from .assets.ZCRMR010_1 import process_ZCRMR010_1
from .assets.ZCRMR010_2 import process_ZCRMR010_2
from .assets.ZCRMR010_3 import process_ZCRMR010_3
from .assets.ZCRMR014 import process_ZCRMR014
from .assets.ZCRMR015 import process_ZCRMR015
from .assets.ZCRMR017 import process_ZCRMR017
from .assets.ZCRMR018 import process_ZCRMR018
from .assets.ZCRMR019 import process_ZCRMR019
from .assets.ZCRMR063 import process_ZCRMR063
from .assets.ZCRMR066 import process_ZCRMR066
from .assets.ZCRMR072 import process_ZCRMR072
from .assets.ZCRMR073 import process_ZCRMR073
from .assets.ZCRMR074 import process_ZCRMR074
from .assets.ZCRMR075 import process_ZCRMR075
from .assets.ZCRMR076 import process_ZCRMR076
from .assets.ZCRMR077 import process_ZCRMR077
from .assets.commission import process_commission
from .assets.demand_customer import process_demand_customers
from .assets.expense import process_expense
from .assets.order import process_order
from .assets.property_unit import process_properties
from .assets.orgchart import process_orgchart
from .assets.project import process_project
from .assets.employee import process_employees
from .assets.customer import process_customers
from .assets.contract import process_contracts
from .assets.account import process_accounts
from .assets.transactions import process_transactions


def get_definitions():
    materialize_all_assets_job = dg.define_asset_job(
        "materialize_all_report_assets",
        selection=[
            process_expense, process_commission,
            process_properties,
            process_order, process_orgchart,
            process_project, process_employees,
            process_customers, process_contracts,
            process_demand_customers, process_accounts,
            process_transactions,
            process_ZCRMR001, process_ZCRMR003,process_ZCRMR004, process_ZCRMR005,process_ZCRMR006,
            process_ZCRMR007,process_ZCRMR010_1,process_ZCRMR010_2,process_ZCRMR010_3,
            process_ZCRMR014, process_ZCRMR015,
            process_ZCRMR017, process_ZCRMR018, process_ZCRMR019,
            process_ZCRMR063,process_ZCRMR066,
            process_ZCRMR072, process_ZCRMR073, process_ZCRMR074, process_ZCRMR075,process_ZCRMR076,process_ZCRMR077
        ]
    )

    daily_schedule = dg.ScheduleDefinition(
        job=materialize_all_assets_job,
        cron_schedule="*/5 * * * *"
    )

    return dg.Definitions(
        assets=[
            process_expense, process_commission,
            process_properties,
            process_order, process_orgchart,
            process_project, process_employees,
            process_customers, process_contracts,
            process_demand_customers, process_accounts,
            process_transactions,
            process_ZCRMR001, process_ZCRMR003,process_ZCRMR004, process_ZCRMR005,process_ZCRMR006,
            process_ZCRMR007,process_ZCRMR010_1,process_ZCRMR010_2,process_ZCRMR010_3,
            process_ZCRMR014,process_ZCRMR015, process_ZCRMR017, process_ZCRMR018,process_ZCRMR019,
            process_ZCRMR063,process_ZCRMR066,
            process_ZCRMR072, process_ZCRMR073, process_ZCRMR074, process_ZCRMR075,process_ZCRMR076,process_ZCRMR077
        ],
        jobs=[materialize_all_assets_job],
        schedules=[daily_schedule],
        executor=multiprocess_executor
    )
