from dagster import asset

from quickstart.common import retry_policy
from quickstart.transform_report_data.assets.base_asset import BaseAsset
from quickstart.transform_report_data.assets.contract import process_contracts
from quickstart.transform_report_data.assets.customer import process_customers
from quickstart.transform_report_data.assets.employee import process_employees
from quickstart.transform_report_data.assets.order import process_order
from quickstart.transform_report_data.assets.orgchart import process_orgchart
from quickstart.transform_report_data.assets.project import process_project
from quickstart.transform_report_data.assets.property_unit import process_properties


class ZCRMR063(BaseAsset):

    def __init__(self):
        super().__init__("ZCRMR063")

    def create_backup(self) -> None:
        sql = f"""CREATE TABLE {self.table_name}_backup ENGINE = MergeTree()
                     PRIMARY KEY (propertyUnitIdentity, orgchartCode, projectName, customerName, depositAmount,
                     dateKey)
                     ORDER BY (propertyUnitIdentity, orgchartCode, projectName, customerName, depositAmount,
                  dateKey)
                SETTINGS index_granularity = 8192
                AS SELECT * FROM {self.table_name}"""
        self.client.command(sql)

    def insert_data(self) -> None:
        sql = f"""INSERT INTO ZCRMR063 
          select oc.code                                                                                           as orgchartCode,
       oc.name                                                                                           as orgchartName,
       e.code                                                                                            as employeeCode,
       p.code                                                                                            as projectCode,
       p.name                                                                                            as projectName,
       pu.property_identity                                                                              as propertyUnitIdentity,
       pu.code                                                                                           as propertyCode,
       pu.floor                                                                                          as floor,
       pu.bedroom,
       pu.direction,
       pu.view,
       pu.inside_area                                                                                    as insideArea,
       pu.area,
       o.code                                                                                            as transactionCode,
       multiIf(ct.type = 'deposit', 'TLYHDC', 'TLYHDNT')                                                 as statusCode,
       multiIf(ct.type = 'deposit', 'Thanh lý hợp đồng giữ chỗ đặt cọc', 'Thanh lý hợp đồng nguyên tắc') as statusName,
       ct.update_date                                                                                    as statusDate,
       o.note                                                                                            as note,
       c.code                                                                                            as customerCode,
       c.name                                                                                            as customerName,
       c.name                                                                                            as customerNameContract,
       pu.price                                                                                          as price,
       pu.price_vat                                                                                      as priceVat,
       pu.land_price                                                                                     as landPrice,
       pu.price_above                                                                                    as priceAbove,
       pu.price_above_vat                                                                                as priceAboveVat,
       'Phương Thức thanh toán'                                                                          as paymentMethod,
       -1                                                                                                as valueByPaymentPlan,
       -1                                                                                                as discountValue,
       ' '                                                                                               as discountDescription,
       -1                                                                                                as finalAmount,
       -1                                                                                                as vatAmount,
       (finalAmount + vatAmount)                                                                         as finalAmountVat,
       -1                                                                                                as landUseFee,
       ct.maintenance_fee                                                                                as maintenanceFee,
       (finalAmountVat + landUseFee + maintenanceFee)                                                    as totalAmount,
       e.name                                                                                            as employeeName,
       parseDateTimeBestEffort(o.created_date)                                                           as depositDate,
       o.total_amount_deposit                                                                            as depositAmount,
       (totalAmount + sum_amount_contract.money)                                                       as totalAmountPayment,
       if(ct.type = 'deposit', parseDateTimeBestEffort(o.modified_date),
          null)                                                                                          as depositCancelDate,
       if(ct.type != 'rent', parseDateTimeBestEffort(o.modified_date), null)                             as contractCancelDate,
       o.date_key                                                                                        AS dateKey,
       d.full_date                                                                                       AS fullDate,
       d.date_string                                                                                     AS dateString,
       d.year                                                                                            AS year,
       d.quarter                                                                                         AS quarter,
       d.quarter_name                                                                                    AS quarterName,
       d.month                                                                                           AS month,
       d.month_name                                                                                      AS monthName,
       d.month_short_name                                                                                AS monthShortName,
       d.week_of_year                                                                                    AS weekOfYear,
       d.day_of_week                                                                                     AS dayOfWeek,
       d.day_of_month                                                                                    AS dayOfMonth,
       d.day_of_year                                                                                     AS dayOfYear,
       d.day_name                                                                                        AS dayName
from fact_orders o
         left join {self.database}.dim_customers c on o.customer_code = c.code and c.is_current = 1
         left join {self.database}.dim_orgcharts oc on o.pos_id = oc.id and oc.is_current = 1
         left join {self.database}.dim_projects p on o.project_id = p.id and p.is_current = 1
         left join {self.database}.dim_property_units pu on o.property_unit_id = pu.id and pu.is_current = 1
         left join {self.database}.dim_employees e on o.employee_id = e.id and e.is_current = 1
         join {self.database}.dim_contracts ct on o.contract_id = ct.id and ct.is_current = 1
         left join (select sum(money) as money, contract_id
                    from {self.database}.fact_transactions
                    where type = 'PRIMARY_CONTRACT'
                    group by contract_id) as sum_amount_contract on o.contract_id = sum_amount_contract.contract_id
         LEFT JOIN {self.database}.dim_date AS d ON o.date_key = d.date_key 
where o.ticket_type = 'YCDC'
  and ct.status = 'liquidated'
  and o.customer_code != ''
        """
        self.client.command(sql)


@asset(
    deps=[
        process_properties,
        process_order,
        process_orgchart,
        process_project,
        process_employees,
        process_customers,
        process_contracts
    ],
    group_name="report_table",
    retry_policy=retry_policy.DB_RETRY_POLICY
)
def process_ZCRMR063() -> None:
    report = ZCRMR063()
    report.process()
