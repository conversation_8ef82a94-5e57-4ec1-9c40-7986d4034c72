from dagster import asset

from quickstart.common import retry_policy
from quickstart.transform_report_data.assets.base_asset import BaseAsset
from quickstart.transform_report_data.assets.customer import process_customers
from quickstart.transform_report_data.assets.employee import process_employees
from quickstart.transform_report_data.assets.order import process_order
from quickstart.transform_report_data.assets.orgchart import process_orgchart
from quickstart.transform_report_data.assets.project import process_project
from quickstart.transform_report_data.assets.property_unit import process_properties


class ZCRMR076(BaseAsset):

    def __init__(self):
        super().__init__("ZCRMR076")

    def create_backup(self) -> None:
        sql = f"""CREATE TABLE {self.table_name}_backup ENGINE = MergeTree()
                       PRIMARY KEY (propertyUnitIdentity, orgchartCode, projectName, customerName, amountDeposit,
                                    dateKey)
        ORDER BY (propertyUnitIdentity, orgchartCode, projectName, customerName, amountDeposit, dateKey)
                SETTINGS index_granularity = 8192
                AS SELECT * FROM {self.table_name}"""
        self.client.command(sql)

    def insert_data(self) -> None:
        sql = f"""INSERT INTO ZCRMR076 
          select oc.code                                 as orgchartCode,
       oc.name                                 as orgchartName,
       e.code                                  as employeeCode,
       p.code                                  as projectCode,
       p.name                                  as projectName,
       pu.property_identity                    as propertyUnitIdentity,
       c.name                                  as customerName,
       c.full_address                          as customerAddress,
       c.phone                                 as customerPhone,
       parseDateTimeBestEffort(ct.signed_date) as signedDate,
       o.total_amount_deposit                  as amountDeposit,
       ' '                                     as cancelReason,
       ' '                                     as posConfirm,
       ' '                                     as employeeConfirm,
       o.date_key                              AS dateKey,
       d.full_date                             AS fullDate,
       d.date_string                           AS dateString,
       d.year                                  AS year,
       d.quarter                               AS quarter,
       d.quarter_name                          AS quarterName,
       d.month                                 AS month,
       d.month_name                            AS monthName,
       d.month_short_name                      AS monthShortName,
       d.week_of_year                          AS weekOfYear,
       d.day_of_week                           AS dayOfWeek,
       d.day_of_month                          AS dayOfMonth,
       d.day_of_year                           AS dayOfYear,
       d.day_name                              AS dayName,
       d.day_short_name                        AS dayShortName,
       d.fiscal_year                           AS fiscalYear,
       d.fiscal_quarter                        AS fiscalQuarter,
       d.fiscal_month                          AS fiscalMonth,
       d.is_weekend                            AS isWeekend,
       d.is_holiday                            AS isHoliday,
       d.is_business_day                       AS isBusinessDay,
       d.is_last_day_of_month                  AS isLastDayOfMonth,
       d.days_in_month                         AS daysInMonth,
       d.year_month_number                     AS yearMonthNumber,
       d.year_quarter_number                   AS yearQuarterNumber,
       d.previous_day_date_key                 AS previousDayDateKey,
       d.next_day_date_key                     AS nextDayDateKey
from {self.database}.fact_orders as o
         left join {self.database}.dim_property_units pu on o.property_unit_id = pu.id and pu.is_current = 1
         left join {self.database}.dim_customers c on o.customer_code = c.code and c.is_current = 1
         left join {self.database}.dim_orgcharts oc on o.pos_id = oc.id and oc.is_current = 1
         left join {self.database}.dim_employees e on o.employee_id = e.id and e.is_current = 1
         left join {self.database}.dim_projects p on o.project_id = p.id and p.is_current = 1
              join {self.database}.dim_contracts ct on o.contract_id = ct.id and ct.is_current = 1
         LEFT JOIN {self.database}.dim_date AS d ON o.date_key = d.date_key 
where o.ticket_type = 'YCDC'
  and o.status = 'CS_APPROVED_CANCEL_REQUESTED'
  and o.customer_code != ''
        """
        self.client.command(sql)


@asset(
    deps=[
        process_properties,
        process_order,
        process_orgchart,
        process_project,
        process_employees,
        process_customers
    ],
    group_name="report_table",
    retry_policy=retry_policy.DB_RETRY_POLICY
)
def process_ZCRMR076() -> None:
    report = ZCRMR076()
    report.process()
