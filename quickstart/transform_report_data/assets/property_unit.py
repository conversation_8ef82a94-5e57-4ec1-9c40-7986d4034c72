from dagster import asset
from quickstart.common import retry_policy
from quickstart.transform_report_data.assets.base_asset import BaseAsset


class PropertyUnit(BaseAsset):

    def __init__(self):
        super().__init__("dim_property_units")

    def create_backup(self) -> None:
        sql = f"""CREATE TABLE {self.table_name}_backup ENGINE = MergeTree()
                PRIMARY KEY id
                ORDER BY id
                SETTINGS index_granularity = 8192
                AS SELECT * FROM {self.table_name}"""
        self.client.command(sql)

    def insert_data(self) -> None:
        sql = f"""insert into dim_property_units
        SELECT o.id,
       JSONExtract(fullDocument, 'code', 'String'),
       JSONExtract(fullDocument, 'active', 'String'),
           if(
        JSONExtract(fullDocument, 'price', 'String') = '' OR JSONExtract(fullDocument, 'price', 'String') IS NULL,
        '0',
        JSONExtract(fullDocument, 'price', 'String')) AS price,
        if(JSONExtract(fullDocument, 'priceVat', 'String') = '' OR JSONExtract(fullDocument, 'priceVat', 'String') IS NULL,
        '0',
        JSONExtract(fullDocument, 'priceVat', 'String')) AS priceVat,
       1,
       JSONExtract(JSONExtract(fullDocument, 'project', 'String'), 'id', 'String'),
       JSONExtract(fullDocument, 'shortCode', 'String'),
       JSONExtract(JSONExtract(fullDocument, 'contract', 'String'), 'id', 'String'),
       valid_from,
       if(o.valid_from = latest.latest_valid_from, 1, 0)                            AS is_current,
       if(o.valid_from != latest.latest_valid_from, latest.latest_valid_from, NULL) AS valid_to,
       attr['value'],
       JSONExtract(fullDocument, 'floor', 'Int32')      as floor,
       JSONExtract(fullDocument, 'bedroom', 'Int32')    as bedroom,
       JSONExtract(fullDocument, 'insideArea', 'Float32') as inside_area,
       JSONExtract(fullDocument, 'area', 'Float32')       as area,
       JSONExtract(fullDocument, 'landPrice', 'Int128')       as land_price,
       JSONExtract(fullDocument, 'priceAbove', 'Int128')       as price_above,
       JSONExtract(fullDocument, 'priceAboveVat', 'Int128')       as price_above_vat,
       JSONExtract(fullDocument, 'housePriceVat', 'Int128')       as house_price_vat,
       JSONExtract(fullDocument, 'housePrice', 'Int128')       as house_price,
       JSONExtract(fullDocument, 'direction', 'String')       as direction,
       JSONExtract(fullDocument, 'view', 'String')       as view
FROM {self.database}.property_units AS o
    ARRAY JOIN JSONExtract(fullDocument, 'attributes', 'Array(Map(String, String))') AS attr
     JOIN
     (SELECT id,
                max(valid_from) AS latest_valid_from
         FROM {self.database}.property_units
         GROUP BY id) AS latest ON o.id = latest.id AND (o.valid_from = latest_valid_from)
WHERE (operationType in ('insert', 'update')) and attr['attributeId'] = '945c3fae-bf91-4fef-95a1-9e58a3a3233c'
        """
        self.client.command(sql)


@asset(
    group_name="dim_fact_table",
    retry_policy=retry_policy.DB_RETRY_POLICY
)
def process_properties() -> None:
    property_units = PropertyUnit()
    property_units.process()
