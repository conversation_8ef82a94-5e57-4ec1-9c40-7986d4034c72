from dagster import asset
from quickstart.common import retry_policy
from quickstart.transform_report_data.assets.base_asset import BaseAsset

class Order(BaseAsset):
    """Asset for processing order data in fact_transactions table"""
    
    def __init__(self):
        super().__init__("fact_transactions")

    def create_backup(self) -> None:
        """Create backup of fact_transactions table with specific ordering"""
        sql = f"""CREATE TABLE {self.table_name}_backup 
                 ENGINE = MergeTree() PRIMARY KEY (id, status, type, date_key)
                 ORDER BY (id, status, type, date_key)
                SETTINGS index_granularity = 8192
                AS SELECT * FROM {self.table_name}"""
        self.client.command(sql)

    def insert_data(self) -> None:
        """Insert transformed data into fact_transactions table"""
        sql = f"""insert into fact_transactions
SELECT id,
       JSONExtract(fullDocument, 'type', 'String'),
       JSONExtract(fullDocument, 'code', 'String'),
       JSONExtract(fullDocument, 'receiptNum', 'String'),
       JSONExtract(fullDocument, 'money', 'UInt128'),
       JSONExtract(fullDocument, 'totalMoney', 'UInt128'),
       JSONExtract(fullDocument, 'transferedMoney', 'UInt128'),
       JSONExtract(fullDocument, 'transferingMoney', 'UInt128'),
       JSONExtract(fullDocument, 'contractRequiredMoney', 'UInt128'),
       JSONExtract(fullDocument, 'contractReceiptMoney', 'UInt128'),
       JSONExtract(fullDocument, 'totalAmount_Print', 'UInt128'),
       JSONExtract(fullDocument, 'interestAmount', 'UInt128'),
       JSONExtract(fullDocument, 'state', 'String'),
       JSONExtract(fullDocument, 'status', 'String'),
       JSONExtract(fullDocument, 'contractId', 'String'),
       JSONExtract(JSONExtract(fullDocument, 'pos', 'String'), 'id', 'String'),
       JSONExtract(JSONExtract(JSONExtract(fullDocument, 'propertyTicket', 'String'), 'project', 'String'), 'id',
                   'String')                                                                as project_id,
       JSONExtract(JSONExtract(JSONExtract(fullDocument, 'propertyTicket', 'String'), 'customer', 'String'), 'code',
                   'String')                                                                as customer_code,
       JSONExtract(JSONExtract(JSONExtract(fullDocument, 'propertyTicket', 'String'), 'employee', 'String'), 'id',
                   'String')                                                                as consultant_id,
       JSONExtract(JSONExtract(JSONExtract(fullDocument, 'propertyTicket', 'String'), 'propertyUnit', 'String'), 'id',
                   'String')                                                                as property_unit_id,
       JSONExtract(JSONExtract(fullDocument, 'propertyTicket', 'String'), 'bookingTicketCode', 'String') as booking_ticket_code,
       JSONExtract(JSONExtract(fullDocument, 'propertyTicket', 'String'), 'note', 'String') as ticket_note,
       JSONExtract(JSONExtract(JSONExtract(JSONExtract(fullDocument, 'propertyTicket', 'String'), 'customer', 'String'),
                               'bankInfo',
                               'String'), 'accountNumber',
                   'String')                                                                as bank_account_number,
       JSONExtract(JSONExtract(fullDocument, 'propertyTicket', 'String'), 'createdDate',
                   'String')                                                                as ticket_created_date,
       JSONExtract(JSONExtract(JSONExtract(JSONExtract(fullDocument, 'propertyTicket', 'String'), 'customer', 'String'),
                               'bankInfo',
                               'String'), 'NAME', 'String')                                 as bank_name,
       JSONExtract(fullDocument, 'reason', 'String')                                        as note,
       JSONExtract(fullDocument, 'createdAt', 'String')                                   as created_date,
       JSONExtract(fullDocument, 'receiptNum', 'String')                                    as receipt_num,

       date_key,
       valid_from,
       if(t.valid_from != latest.latest_valid_from, latest.latest_valid_from, NULL) AS valid_to,
       if(t.valid_from = latest.latest_valid_from, 1, 0)                            AS is_current,
       JSONExtract(JSONExtract(fullDocument, 'propertyTicket', 'String'), 'escrowTicketCode', 'String') as escrow_ticket_code,
       JSONExtract(JSONExtract(fullDocument, 'propertyTicket', 'String'), 'ticketType', 'String') as ticket_type
FROM {self.database}.transactions AS t
         INNER JOIN
     (
         SELECT id,
                max(valid_from) AS latest_valid_from
         FROM {self.database}.transactions
         GROUP BY id
         ) AS latest ON (t.id = latest.id) AND (t.valid_from = latest.latest_valid_from)
WHERE (operationType in ('insert', 'update'))
        """
        self.client.command(sql)

@asset(
    group_name="dim_fact_table", 
    retry_policy=retry_policy.DB_RETRY_POLICY
)
def process_transactions() -> None:
    """Dagster asset to process order data"""
    orders = Order()
    orders.process()
