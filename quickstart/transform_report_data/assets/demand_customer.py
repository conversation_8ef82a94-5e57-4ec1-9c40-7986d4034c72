from dagster import asset
from quickstart.common import retry_policy
from quickstart.transform_report_data.assets.base_asset import BaseAsset

class DemandCustomer(BaseAsset):

    def __init__(self):
        super().__init__("dim_demand_customers")

    def create_backup(self) -> None:
        sql = f"""CREATE TABLE {self.table_name}_backup ENGINE = MergeTree()
                PRIMARY KEY (id,code,is_current)
                ORDER BY (id,code,is_current)
                SETTINGS index_granularity = 8192
                AS SELECT * FROM {self.table_name}"""
        self.client.command(sql)

    def insert_data(self) -> None:
        sql = f"""insert into dim_demand_customers
        SELECT
       JSONExtract(JSONExtract(fullDocument, 'personalInfo', 'String'), 'name', 'String'),
       JSONExtract(fullDocument, 'code', 'String'),
       JSONExtract(fullDocument, 'id', 'String'),
       J<PERSON>NExtract(fullDocument, 'type', 'String'),
       J<PERSON>NExtract(JSONExtract(fullDocument, 'info', 'String'), 'birthday', 'String'),
              JSONExtract(JSONExtractArrayRaw(JSONExtractString(fullDocument, 'personalInfo'), 'identities')[1], 'value',
                   'String'),
       JSONExtract(JSONExtractArrayRaw(JSONExtractString(fullDocument, 'personalInfo'), 'identities')[1], 'place',
                   'String'),
       JSONExtract(JSONExtractArrayRaw(JSONExtractString(fullDocument, 'personalInfo'), 'identities')[1], 'date',
                   'String'),
       JSONExtract(JSONExtract(JSONExtract(fullDocument, 'info', 'String'), 'address', 'String'), 'fullAddress', 'String'),
       JSONExtract(JSONExtract(fullDocument, 'personalInfo', 'String'), 'phone', 'String'),
       JSONExtract(JSONExtract(fullDocument, 'personalInfo', 'String'), 'email', 'String'),
       JSONExtract(JSONExtract(fullDocument, 'company', 'String'), 'name', 'String'),
       JSONExtract(JSONExtract(fullDocument, 'company', 'String'), 'issueDate', 'String'),
       JSONExtract(JSONExtract(fullDocument, 'company', 'String'), 'issueLocation', 'String'),
       JSONExtract(JSONExtract(fullDocument, 'pos', 'String'), 'id', 'String'),
       JSONExtract(JSONExtract(fullDocument, 'employee', 'String'), 'id', 'String'),
       valid_from,
       JSONExtract(fullDocument, 'taxCode', 'String'),
       if(dc.valid_from = latest.latest_valid_from, 1, 0)                            AS is_current,
       if(dc.valid_from != latest.latest_valid_from, latest.latest_valid_from, NULL) AS valid_to
FROM {self.database}.demand_customers AS dc
          JOIN
     (
         SELECT id,
                max(valid_from) AS latest_valid_from
         FROM {self.database}.demand_customers
         GROUP BY id
         ) AS latest ON dc.id = latest.id AND (dc.valid_from = latest_valid_from)
WHERE (operationType in ('insert', 'update'))
        """
        self.client.command(sql)

@asset(
    group_name="dim_fact_table", 
    retry_policy=retry_policy.DB_RETRY_POLICY
)
def process_demand_customers() -> None:
    demand_customers = DemandCustomer()
    demand_customers.process()
