from dagster import asset
from quickstart.common import retry_policy
from quickstart.transform_report_data.assets.base_asset import BaseAsset
from quickstart.transform_report_data.assets.contract import process_contracts
from quickstart.transform_report_data.assets.demand_customer import process_demand_customers
from quickstart.transform_report_data.assets.employee import process_employees
from quickstart.transform_report_data.assets.order import process_order
from quickstart.transform_report_data.assets.orgchart import process_orgchart
from quickstart.transform_report_data.assets.project import process_project
from quickstart.transform_report_data.assets.property_unit import process_properties


class ZCRMR006(BaseAsset):

    def __init__(self):
        super().__init__("ZCRMR006")

    def create_backup(self) -> None:
        sql = f"""CREATE TABLE {self.table_name}_backup ENGINE = MergeTree()
                ORDER BY (orgchartCode,orgchartName, projectCode, projectName, employeeCode, dateKey)
                PRIMARY KEY (orgchartCode,orgchartName, projectCode, projectName, employeeCode, dateKey)
                SETTINGS index_granularity = 8192
                AS SELECT * FROM {self.table_name}"""
        self.client.command(sql)

    def insert_data(self) -> None:
        sql = f"""INSERT INTO ZCRMR006
                SELECT oc.code        as orgchartCode,
                oc.name as orgchartName,
                p.code         as projectCode,
                p.name         as projectName,
                pu.property_identity        as propertyCode,
                c.name         as customerName,
                c.full_address as customerAddress,
                1              as depositCount,
                o.amount       as amount,
                e.code         as employeeCode,
                multiIf(o.booking_ticket_code != '', 'đặt cọc từ đặt chỗ', o.booking_ticket_code = '', 'Khách hàng chuyển tiền trực tiếp qua DXMT', '')           AS note,
                o.date_key                  AS dateKey,
                d.full_date                 AS fullDate,
                d.date_string               AS dateString,
                d.year                      AS year,
                d.quarter                   AS quarter,
                d.quarter_name              AS quarterName,
                d.month                     AS month,
                d.month_name                AS monthName,
                d.month_short_name          AS monthShortName,
                d.week_of_year              AS weekOfYear,
                d.day_of_week               AS dayOfWeek,
                d.day_of_month              AS dayOfMonth,
                d.day_of_year               AS dayOfYear,
                d.day_name                  AS dayName,
                d.day_short_name            AS dayShortName,
                d.fiscal_year               AS fiscalYear,
                d.fiscal_quarter            AS fiscalQuarter,
                d.fiscal_month              AS fiscalMonth,
                d.is_weekend                AS isWeekend,
                d.is_holiday                AS isHoliday,
                d.is_business_day           AS isBusinessDay,
                d.is_last_day_of_month      AS isLastDayOfMonth,
                d.days_in_month             AS daysInMonth,
                d.year_month_number         AS yearMonthNumber,
                d.year_quarter_number       AS yearQuarterNumber,
                d.previous_day_date_key     AS previousDayDateKey,
                d.next_day_date_key         AS nextDayDateKey
from {self.database}.fact_orders as o
        LEFT JOIN {self.database}.dim_customers c on o.customer_code != '' and o.customer_code = c.code and c.is_current = 1
        LEFT JOIN {self.database}.dim_property_units pu on o.property_unit_id = pu.id and pu.is_current = 1
        LEFT JOIN {self.database}.dim_employees e on o.employee_id = e.id and e.is_current = 1
        LEFT JOIN {self.database}.dim_orgcharts AS oc ON (o.pos_id = oc.id) AND (oc.is_current = 1)
        LEFT JOIN {self.database}.dim_projects AS p ON (pu.project_id = p.id) AND (p.is_current = 1)
        LEFT JOIN {self.database}.dim_date AS d ON o.date_key = d.date_key
where o.ticket_type = 'YCDC' and o.customer_code !='' 
  and o.status in ('SUCCESS','POS_CONFIRM','LOCK_CONFIRM')
  and o.is_current = 1
        """
        self.client.command(sql)


@asset(
    deps=[
        process_properties,
        process_order,
        process_orgchart,
        process_project,
        process_employees,
        process_demand_customers,
        process_contracts
    ],
    group_name="report_table",
    retry_policy=retry_policy.DB_RETRY_POLICY
)
def process_ZCRMR006() -> None:
    report = ZCRMR006()
    report.process()