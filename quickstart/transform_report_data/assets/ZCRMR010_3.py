from dagster import asset
from quickstart.common import retry_policy
from quickstart.transform_report_data.assets.base_asset import BaseAsset
from quickstart.transform_report_data.assets.commission import process_commission
from quickstart.transform_report_data.assets.contract import process_contracts
from quickstart.transform_report_data.assets.demand_customer import process_demand_customers
from quickstart.transform_report_data.assets.employee import process_employees
from quickstart.transform_report_data.assets.expense import process_expense
from quickstart.transform_report_data.assets.order import process_order
from quickstart.transform_report_data.assets.orgchart import process_orgchart
from quickstart.transform_report_data.assets.project import process_project
from quickstart.transform_report_data.assets.property_unit import process_properties


class ZCRMR010_3(BaseAsset):

    def __init__(self):
        super().__init__("ZCRMR010_3")

    def create_backup(self) -> None:
        sql = f"""CREATE TABLE {self.table_name}_backup ENGINE = MergeTree()
                PRIMARY KEY (periodFrom, periodTo, propertyIdentity, orgchartCode, orgchartName, customerName,
                                    nvatPrice, transactionConsultingFee, referencePrice, advanced, advance_3th, dateKey)
        ORDER BY (periodFrom, periodTo, propertyIdentity, orgchartCode, orgchartName, customerName, nvatPrice,
                  transactionConsultingFee, referencePrice, advanced, advance_3th, dateKey)
                SETTINGS index_granularity = 8192
                AS SELECT * FROM {self.table_name}"""
        self.client.command(sql)

    def insert_data(self) -> None:
        pattern = r"^\d{4}-\d{2}-\d{2} → \d{4}-\d{2}-\d{2}$"

        sql = f"""insert into ZCRMR010_3
select fer.property_unit_identity                                                                    as propertyIdentity,
       p.type                                                                                        as propertyType,
       'fer.customer_code'                                                                           as customerCode,
       fer.customer_name                                                                             as customerName,
       fer.nvat_price                                                                                as nvatPrice,
       fer.price                                                                                     as price,
       (fer.transaction_consulting_fee * 1.1)                                                        as transactionConsultingFee,
       (fer.nvat_price + fer.transaction_consulting_fee)                                             as referencePrice,
       fer.revenue_rate                                                                              as revenueRate,
       fer.employee_revenue_rate                                                                     as employeeRevenueRate,
       (referencePrice * revenueRate * employeeRevenueRate)                                          as basicFee,
       fer.transaction_other_fee                                                                     as otherFee,
       (basicFee + otherFee)                                                                         as total,
       (((fer.transaction_register_fee / 1.1) * 0.5) + ((fer.transaction_register_fee / 1.1) * 0.5)) AS advanced,
       (fer.transaction_total - advanced)                                                            AS advance_3th,
       '1975-04-30'                                                                                  as ticketCreatedDate,
       oc.name                                                                                       as orgchartName,
       oc.code                                                                                       as orgchartCode,
       fer.date_key                                                                                  as dateKey,
       parseDateTimeBestEffort(splitByString(' → ', comm.period)[1])                                 AS period_from,
       parseDateTimeBestEffort(splitByString(' → ', comm.period)[2])                                 AS period_to,
       d.full_date                                                                                   AS fullDate,
       d.date_string                                                                                 AS dateString,
       d.year                                                                                        AS year,
       d.quarter                                                                                     AS quarter,
       d.quarter_name                                                                                AS quarterName,
       d.month                                                                                       AS month,
       d.month_name                                                                                  AS monthName,
       d.month_short_name                                                                            AS monthShortName,
       d.week_of_year                                                                                AS weekOfYear,
       d.day_of_week                                                                                 AS dayOfWeek,
       d.day_of_month                                                                                AS dayOfMonth,
       d.day_of_year                                                                                 AS dayOfYear,
       d.day_name                                                                                    AS dayName,
       d.day_short_name                                                                              AS dayShortName,
       d.fiscal_year                                                                                 AS fiscalYear,
       d.fiscal_quarter                                                                              AS fiscalQuarter,
       d.fiscal_month                                                                                AS fiscalMonth,
       d.is_weekend                                                                                  AS isWeekend,
       d.is_holiday                                                                                  AS isHoliday,
       d.is_business_day                                                                             AS isBusinessDay,
       d.is_last_day_of_month                                                                        AS isLastDayOfMonth,
       d.days_in_month                                                                               AS daysInMonth,
       d.year_month_number                                                                           AS yearMonthNumber,
       d.year_quarter_number                                                                         AS yearQuarterNumber,
       d.previous_day_date_key                                                                       AS previousDayDateKey,
       d.next_day_date_key                                                                           AS nextDayDateKey,
       comm.period
from {self.database}.fact_expense_records fer
         left join {self.database}.dim_projects p on fer.project_id = p.id and p.is_current = 1
         left join {self.database}.dim_commissions comm on fer.commission_id = comm.id and comm.is_current = 1
         left join {self.database}.dim_orgcharts oc on comm.pos_id = oc.id and oc.is_current = 1
         left join {self.database}.dim_date d on fer.date_key = d.date_key
where fer.is_current = 1
  and match(comm.period, '{pattern}') = 1  
        """
        self.client.command(sql)


@asset(
    deps=[
        process_commission,
        process_expense

    ],
    group_name="report_table",
    retry_policy=retry_policy.DB_RETRY_POLICY
)
def process_ZCRMR010_3() -> None:
    report = ZCRMR010_3()
    report.process()
